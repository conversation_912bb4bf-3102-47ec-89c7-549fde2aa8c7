using System.ComponentModel.DataAnnotations;

namespace FamilyApp.Models
{
    public class Family
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string FamilyName { get; set; } = string.Empty;
        
        // Foreign keys for father and mother
        public int? FatherId { get; set; }
        public Member? Father { get; set; }
        
        public int? MotherId { get; set; }
        public Member? Mother { get; set; }
        
        // Navigation property for all members in this family
        public ICollection<Member> Members { get; set; } = new List<Member>();
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
