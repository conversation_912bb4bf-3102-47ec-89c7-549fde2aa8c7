using System.ComponentModel.DataAnnotations;

namespace FamilyApp.Models
{
    public enum Gender
    {
        Male = 1,   // ذكر
        Female = 2  // أنثى
    }

    public class Member
    {
        public int Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        [Required]
        public Gender Gender { get; set; }
        
        public DateTime? DateOfBirth { get; set; }
        
        // Foreign key to Family
        public int? FamilyId { get; set; }
        public Family? Family { get; set; }
        
        // Navigation properties for families where this member is father or mother
        public ICollection<Family> FamiliesAsFather { get; set; } = new List<Family>();
        public ICollection<Family> FamiliesAsMother { get; set; } = new List<Family>();
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
