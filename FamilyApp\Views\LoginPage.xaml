<Window x:Class="FamilyApp.Views.LoginPage"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام الأنساب - تسجيل الدخول" 
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <materialDesign:Card Margin="20" Padding="30">
        <StackPanel>
            <!-- Header -->
            <materialDesign:PackIcon Kind="Family" 
                                   Width="64" Height="64" 
                                   HorizontalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   Margin="0,0,0,20"/>
            
            <TextBlock Text="نظام الأنساب" 
                      FontSize="24" 
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,10"/>
            
            <TextBlock Text="تسجيل الدخول" 
                      FontSize="16" 
                      HorizontalAlignment="Center"
                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                      Margin="0,0,0,30"/>

            <!-- Username Field -->
            <TextBox materialDesign:HintAssist.Hint="اسم المستخدم"
                    materialDesign:HintAssist.IsFloating="True"
                    Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                    Margin="0,0,0,20"
                    FontSize="14"/>

            <!-- Password Field -->
            <PasswordBox x:Name="PasswordBox"
                        materialDesign:HintAssist.Hint="كلمة المرور"
                        materialDesign:HintAssist.IsFloating="True"
                        Margin="0,0,0,20"
                        FontSize="14"
                        PasswordChanged="PasswordBox_PasswordChanged"/>

            <!-- Error Message -->
            <TextBlock Text="{Binding ErrorMessage}"
                      Foreground="Red"
                      FontSize="12"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,20"
                      Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

            <!-- Login Button -->
            <Button Content="تسجيل الدخول"
                   Command="{Binding LoginCommand}"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Height="40"
                   FontSize="14"
                   Margin="0,0,0,20"
                   IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"/>

            <!-- Loading Indicator -->
            <ProgressBar IsIndeterminate="True"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Margin="0,10,0,0"/>

            <!-- Default Credentials Info -->
            <Border Background="{DynamicResource MaterialDesignDivider}"
                   Padding="10"
                   Margin="0,20,0,0"
                   CornerRadius="4">
                <StackPanel>
                    <TextBlock Text="بيانات الدخول الافتراضية:"
                              FontWeight="Bold"
                              FontSize="12"
                              Margin="0,0,0,5"/>
                    <TextBlock Text="اسم المستخدم: admin"
                              FontSize="11"/>
                    <TextBlock Text="كلمة المرور: admin123"
                              FontSize="11"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </materialDesign:Card>
</Window>
