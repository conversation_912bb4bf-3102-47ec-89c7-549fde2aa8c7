using Microsoft.EntityFrameworkCore;
using FamilyApp.Database;
using FamilyApp.Models;

namespace FamilyApp.Services
{
    public class FamilyTreeNode
    {
        public Member Member { get; set; } = new();
        public List<FamilyTreeNode> Children { get; set; } = new();
        public int Level { get; set; }
    }

    public class ReportService
    {
        private readonly AppDbContext _context;

        public ReportService(AppDbContext context)
        {
            _context = context;
        }

        // Generate family tree for a specific family
        public async Task<FamilyTreeNode?> GenerateFamilyTreeAsync(int familyId)
        {
            var family = await _context.Families
                .Include(f => f.Father)
                .Include(f => f.Mother)
                .Include(f => f.Members)
                .FirstOrDefaultAsync(f => f.Id == familyId);

            if (family == null) return null;

            // Start with the father as root if available, otherwise mother
            var root = family.Father ?? family.Mother;
            if (root == null) return null;

            return await BuildFamilyTreeAsync(root, 0);
        }

        // Find the root ancestor of a member
        public async Task<Member?> FindRootAncestorAsync(int memberId)
        {
            var member = await _context.Members
                .Include(m => m.Family)
                .ThenInclude(f => f!.Father)
                .Include(m => m.Family)
                .ThenInclude(f => f!.Mother)
                .FirstOrDefaultAsync(m => m.Id == memberId);

            if (member == null) return null;

            var current = member;
            var visited = new HashSet<int>();

            // Traverse up the family tree to find the root
            while (current != null && !visited.Contains(current.Id))
            {
                visited.Add(current.Id);

                // Check if this member is a father or mother in any family
                var parentFamily = await _context.Families
                    .Include(f => f.Father)
                    .Include(f => f.Mother)
                    .FirstOrDefaultAsync(f => f.FatherId == current.Id || f.MotherId == current.Id);

                if (parentFamily == null)
                {
                    // This member is not a parent, check if they belong to a family
                    if (current.Family?.Father != null)
                    {
                        current = current.Family.Father;
                    }
                    else if (current.Family?.Mother != null)
                    {
                        current = current.Family.Mother;
                    }
                    else
                    {
                        break; // No more ancestors
                    }
                }
                else
                {
                    // This member is a parent, check if they have parents
                    var memberFamily = await _context.Members
                        .Include(m => m.Family)
                        .ThenInclude(f => f!.Father)
                        .Include(m => m.Family)
                        .ThenInclude(f => f!.Mother)
                        .FirstOrDefaultAsync(m => m.Id == current.Id);

                    if (memberFamily?.Family?.Father != null && memberFamily.Family.Father.Id != current.Id)
                    {
                        current = memberFamily.Family.Father;
                    }
                    else if (memberFamily?.Family?.Mother != null && memberFamily.Family.Mother.Id != current.Id)
                    {
                        current = memberFamily.Family.Mother;
                    }
                    else
                    {
                        break; // This is the root
                    }
                }
            }

            return current;
        }

        // Find all descendants (children and grandchildren) of a member
        public async Task<List<Member>> FindDescendantsAsync(int memberId)
        {
            var descendants = new List<Member>();
            var visited = new HashSet<int>();

            await FindDescendantsRecursiveAsync(memberId, descendants, visited);

            return descendants;
        }

        private async Task FindDescendantsRecursiveAsync(int memberId, List<Member> descendants, HashSet<int> visited)
        {
            if (visited.Contains(memberId)) return;
            visited.Add(memberId);

            // Find families where this member is father or mother
            var families = await _context.Families
                .Include(f => f.Members)
                .Where(f => f.FatherId == memberId || f.MotherId == memberId)
                .ToListAsync();

            foreach (var family in families)
            {
                foreach (var child in family.Members)
                {
                    if (!descendants.Any(d => d.Id == child.Id))
                    {
                        descendants.Add(child);
                        // Recursively find descendants of this child
                        await FindDescendantsRecursiveAsync(child.Id, descendants, visited);
                    }
                }
            }
        }

        private async Task<FamilyTreeNode> BuildFamilyTreeAsync(Member member, int level)
        {
            var node = new FamilyTreeNode
            {
                Member = member,
                Level = level
            };

            // Find families where this member is father or mother
            var families = await _context.Families
                .Include(f => f.Members)
                .Where(f => f.FatherId == member.Id || f.MotherId == member.Id)
                .ToListAsync();

            foreach (var family in families)
            {
                foreach (var child in family.Members)
                {
                    var childNode = await BuildFamilyTreeAsync(child, level + 1);
                    node.Children.Add(childNode);
                }
            }

            return node;
        }
    }
}
