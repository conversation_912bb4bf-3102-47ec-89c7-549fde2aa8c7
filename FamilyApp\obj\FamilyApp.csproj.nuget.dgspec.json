{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\Family\\v1\\FamilyApp\\FamilyApp.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\Family\\v1\\FamilyApp\\FamilyApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\Family\\v1\\FamilyApp\\FamilyApp.csproj", "projectName": "FamilyApp", "projectPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\Family\\v1\\FamilyApp\\FamilyApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\vscode\\prcsrly\\Family\\v1\\FamilyApp\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.13, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.13, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.13, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.3, )"}, "xunit": {"target": "Package", "version": "[2.6.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}