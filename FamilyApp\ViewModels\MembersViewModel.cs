using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public class MembersViewModel : BaseViewModel
    {
        private readonly DataService _dataService;
        private ObservableCollection<Member> _members = new();
        private ObservableCollection<Family> _availableFamilies = new();
        private Member? _selectedMember;
        private bool _isLoading = false;
        private string _errorMessage = string.Empty;

        // Form fields
        private string _fullName = string.Empty;
        private Gender _selectedGender = Gender.Male;
        private DateTime? _dateOfBirth;
        private DateTime? _dateOfDeath;
        private string _graveNumber = string.Empty;
        private string _gravePlace = string.Empty;
        private string _picturePath = string.Empty;
        private Family? _selectedFamily;
        private bool _isEditMode = false;
        private string _searchTerm = string.Empty;

        public MembersViewModel(DataService dataService)
        {
            _dataService = dataService;

            // Initialize commands
            LoadMembersCommand = new RelayCommand(async () => await LoadMembersAsync());
            AddMemberCommand = new RelayCommand(() => AddMember());
            EditMemberCommand = new RelayCommand<Member>(EditMember);
            DeleteMemberCommand = new RelayCommand<Member>(async member => await DeleteMemberAsync(member));
            SaveMemberCommand = new RelayCommand(async () => await SaveMemberAsync(), CanSaveMember);
            CancelEditCommand = new RelayCommand(CancelEdit);
            SearchMembersCommand = new RelayCommand(async () => await SearchMembersAsync());

            // Load initial data
            _ = Task.Run(async () => await LoadMembersAsync());
            _ = Task.Run(async () => await LoadAvailableFamiliesAsync());
        }

        // Properties
        public ObservableCollection<Member> Members
        {
            get => _members;
            set => SetProperty(ref _members, value);
        }

        public ObservableCollection<Family> AvailableFamilies
        {
            get => _availableFamilies;
            set => SetProperty(ref _availableFamilies, value);
        }

        public Member? SelectedMember
        {
            get => _selectedMember;
            set => SetProperty(ref _selectedMember, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string FullName
        {
            get => _fullName;
            set => SetProperty(ref _fullName, value);
        }

        public Gender SelectedGender
        {
            get => _selectedGender;
            set => SetProperty(ref _selectedGender, value);
        }

        public DateTime? DateOfBirth
        {
            get => _dateOfBirth;
            set => SetProperty(ref _dateOfBirth, value);
        }

        public DateTime? DateOfDeath
        {
            get => _dateOfDeath;
            set => SetProperty(ref _dateOfDeath, value);
        }

        public string GraveNumber
        {
            get => _graveNumber;
            set => SetProperty(ref _graveNumber, value);
        }

        public string GravePlace
        {
            get => _gravePlace;
            set => SetProperty(ref _gravePlace, value);
        }

        public string PicturePath
        {
            get => _picturePath;
            set => SetProperty(ref _picturePath, value);
        }

        public Family? SelectedFamily
        {
            get => _selectedFamily;
            set => SetProperty(ref _selectedFamily, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public string SearchTerm
        {
            get => _searchTerm;
            set => SetProperty(ref _searchTerm, value);
        }

        public Array GenderOptions => Enum.GetValues(typeof(Gender));

        // Commands
        public ICommand LoadMembersCommand { get; }
        public ICommand AddMemberCommand { get; }
        public ICommand EditMemberCommand { get; }
        public ICommand DeleteMemberCommand { get; }
        public ICommand SaveMemberCommand { get; }
        public ICommand CancelEditCommand { get; }
        public ICommand SearchMembersCommand { get; }

        // Methods
        private async Task LoadMembersAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var members = await _dataService.GetAllMembersAsync();
                Members = new ObservableCollection<Member>(members);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل الأعضاء: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadAvailableFamiliesAsync()
        {
            try
            {
                var families = await _dataService.GetAllFamiliesAsync();

                // Ensure we update on UI thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    AvailableFamilies.Clear();
                    foreach (var family in families)
                    {
                        AvailableFamilies.Add(family);
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل العائلات: {ex.Message}";
            }
        }

        private void AddMember()
        {
            ClearForm();
            IsEditMode = true;
        }

        private void EditMember(Member? member)
        {
            if (member == null) return;

            SelectedMember = member;
            FullName = member.FullName;
            SelectedGender = member.Gender;
            DateOfBirth = member.DateOfBirth;
            DateOfDeath = member.DateOfDeath;
            GraveNumber = member.GraveNumber ?? string.Empty;
            GravePlace = member.GravePlace ?? string.Empty;
            PicturePath = member.PicturePath ?? string.Empty;
            SelectedFamily = member.Family;
            IsEditMode = true;
        }

        private async Task SaveMemberAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                if (SelectedMember == null)
                {
                    // Create new member
                    var newMember = new Member
                    {
                        FullName = FullName,
                        Gender = SelectedGender,
                        DateOfBirth = DateOfBirth,
                        DateOfDeath = DateOfDeath,
                        GraveNumber = string.IsNullOrWhiteSpace(GraveNumber) ? null : GraveNumber,
                        GravePlace = string.IsNullOrWhiteSpace(GravePlace) ? null : GravePlace,
                        PicturePath = string.IsNullOrWhiteSpace(PicturePath) ? null : PicturePath,
                        FamilyId = SelectedFamily?.Id
                    };

                    await _dataService.CreateMemberAsync(newMember);
                }
                else
                {
                    // Update existing member
                    SelectedMember.FullName = FullName;
                    SelectedMember.Gender = SelectedGender;
                    SelectedMember.DateOfBirth = DateOfBirth;
                    SelectedMember.DateOfDeath = DateOfDeath;
                    SelectedMember.GraveNumber = string.IsNullOrWhiteSpace(GraveNumber) ? null : GraveNumber;
                    SelectedMember.GravePlace = string.IsNullOrWhiteSpace(GravePlace) ? null : GravePlace;
                    SelectedMember.PicturePath = string.IsNullOrWhiteSpace(PicturePath) ? null : PicturePath;
                    SelectedMember.FamilyId = SelectedFamily?.Id;

                    await _dataService.UpdateMemberAsync(SelectedMember);
                }

                await LoadMembersAsync();
                CancelEdit();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ العضو: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteMemberAsync(Member? member)
        {
            if (member == null) return;

            try
            {
                IsLoading = true;
                await _dataService.DeleteMemberAsync(member.Id);
                await LoadMembersAsync();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حذف العضو: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SearchMembersAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchTerm))
                {
                    await LoadMembersAsync();
                    return;
                }

                IsLoading = true;
                ErrorMessage = string.Empty;

                var members = await _dataService.SearchMembersAsync(SearchTerm);
                Members = new ObservableCollection<Member>(members);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في البحث: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            IsEditMode = false;
            ClearForm();
        }

        private void ClearForm()
        {
            SelectedMember = null;
            FullName = string.Empty;
            SelectedGender = Gender.Male;
            DateOfBirth = null;
            DateOfDeath = null;
            GraveNumber = string.Empty;
            GravePlace = string.Empty;
            PicturePath = string.Empty;
            SelectedFamily = null;
        }

        private bool CanSaveMember()
        {
            return !string.IsNullOrWhiteSpace(FullName) && !IsLoading;
        }
    }
}
