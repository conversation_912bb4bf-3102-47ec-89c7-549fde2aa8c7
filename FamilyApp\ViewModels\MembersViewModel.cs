using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public partial class MembersViewModel : BaseViewModel
    {
        private readonly DataService _dataService;

        [ObservableProperty]
        private ObservableCollection<Member> members = new();

        [ObservableProperty]
        private ObservableCollection<Family> availableFamilies = new();

        [ObservableProperty]
        private Member? selectedMember;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private string searchTerm = string.Empty;

        // Events
        public event EventHandler? AddMemberRequested;
        public event EventHandler<Member>? ViewImageRequested;

        public MembersViewModel(DataService dataService)
        {
            _dataService = dataService;

            // Load initial data
            LoadDataAsync();
        }

        [RelayCommand]
        private async Task RefreshAsync()
        {
            await LoadMembersAsync();
            await LoadAvailableFamiliesAsync();
        }

        [RelayCommand]
        private void AddMember()
        {
            AddMemberRequested?.Invoke(this, EventArgs.Empty);
        }

        [RelayCommand]
        private void ViewImage(Member? member)
        {
            if (member != null)
            {
                ViewImageRequested?.Invoke(this, member);
            }
        }

        [RelayCommand]
        private async Task EditMemberAsync(Member? member)
        {
            if (member == null) return;

            try
            {
                // For now, we'll open the add member window with the member data
                // In a real app, you'd create an EditMemberWindow
                var addMemberViewModel = new AddMemberViewModel(_dataService);

                // Pre-populate the form with existing data
                addMemberViewModel.FullName = member.FullName;
                addMemberViewModel.IsMale = member.Gender == Gender.Male;
                addMemberViewModel.IsFemale = member.Gender == Gender.Female;
                addMemberViewModel.DateOfBirth = member.DateOfBirth;
                addMemberViewModel.DateOfDeath = member.DateOfDeath;
                addMemberViewModel.GraveNumber = member.GraveNumber ?? string.Empty;
                addMemberViewModel.GravePlace = member.GravePlace ?? string.Empty;
                addMemberViewModel.PicturePath = member.PicturePath ?? string.Empty;

                // TODO: Set selected family

                ErrorMessage = "ميزة التعديل ستكون متاحة قريباً";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تعديل العضو: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task DeleteMemberAsync(Member? member)
        {
            if (member == null) return;

            try
            {
                // Show confirmation dialog
                var result = System.Windows.MessageBox.Show(
                    $"هل أنت متأكد من حذف العضو '{member.FullName}'؟",
                    "تأكيد الحذف",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    await _dataService.DeleteMemberAsync(member.Id);
                    await LoadMembersAsync();
                    ErrorMessage = $"تم حذف العضو '{member.FullName}' بنجاح";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حذف العضو: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task SearchMembersAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchTerm))
                {
                    await LoadMembersAsync();
                    return;
                }

                IsLoading = true;
                ErrorMessage = string.Empty;

                var members = await _dataService.SearchMembersAsync(SearchTerm);
                Members = new ObservableCollection<Member>(members);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في البحث: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }



        // Methods
        private async void LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                await LoadMembersAsync();
                await Task.Delay(100); // Small delay to avoid threading issues
                await LoadAvailableFamiliesAsync();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadMembersAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var members = await _dataService.GetAllMembersAsync();
                Members = new ObservableCollection<Member>(members);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل الأعضاء: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadAvailableFamiliesAsync()
        {
            try
            {
                var families = await _dataService.GetAllFamiliesAsync();
                AvailableFamilies = new ObservableCollection<Family>(families);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل العائلات: {ex.Message}";
            }
        }


    }
}
