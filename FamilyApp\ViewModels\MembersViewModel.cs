using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public partial class MembersViewModel : BaseViewModel
    {
        private readonly DataService _dataService;

        [ObservableProperty]
        private ObservableCollection<Member> members = new();

        [ObservableProperty]
        private ObservableCollection<Family> availableFamilies = new();

        [ObservableProperty]
        private Member? selectedMember;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private string searchTerm = string.Empty;

        // Events
        public event EventHandler? AddMemberRequested;
        public event EventHandler<Member>? ViewImageRequested;

        public MembersViewModel(DataService dataService)
        {
            _dataService = dataService;

            // Load initial data
            LoadDataAsync();
        }

        [RelayCommand]
        private async Task RefreshAsync()
        {
            await LoadMembersAsync();
            await LoadAvailableFamiliesAsync();
        }

        [RelayCommand]
        private void AddMember()
        {
            AddMemberRequested?.Invoke(this, EventArgs.Empty);
        }

        [RelayCommand]
        private void ViewImage(Member? member)
        {
            if (member != null)
            {
                ViewImageRequested?.Invoke(this, member);
            }
        }

        [RelayCommand]
        private async Task SearchMembersAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchTerm))
                {
                    await LoadMembersAsync();
                    return;
                }

                IsLoading = true;
                ErrorMessage = string.Empty;

                var members = await _dataService.SearchMembersAsync(SearchTerm);
                Members = new ObservableCollection<Member>(members);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في البحث: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }



        // Methods
        private async void LoadDataAsync()
        {
            await LoadMembersAsync();
            await LoadAvailableFamiliesAsync();
        }

        private async Task LoadMembersAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var members = await _dataService.GetAllMembersAsync();
                Members = new ObservableCollection<Member>(members);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل الأعضاء: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadAvailableFamiliesAsync()
        {
            try
            {
                var families = await _dataService.GetAllFamiliesAsync();
                AvailableFamilies = new ObservableCollection<Family>(families);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل العائلات: {ex.Message}";
            }
        }


    }
}
