using System.Windows.Input;
using FamilyApp.Views;
using Microsoft.Extensions.DependencyInjection;

namespace FamilyApp.ViewModels
{
    public class HomeViewModel : BaseViewModel
    {
        private readonly IServiceProvider _serviceProvider;
        private object? _currentView;
        private string _selectedSection = "العائلات";

        public HomeViewModel(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            
            // Initialize commands
            NavigateToFamiliesCommand = new RelayCommand(() => NavigateToSection("العائلات"));
            NavigateToMembersCommand = new RelayCommand(() => NavigateToSection("الأفراد"));
            NavigateToReportsCommand = new RelayCommand(() => NavigateToSection("التقارير"));
            
            // Set default view
            NavigateToSection("العائلات");
        }

        public object? CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        public string SelectedSection
        {
            get => _selectedSection;
            set => SetProperty(ref _selectedSection, value);
        }

        public ICommand NavigateToFamiliesCommand { get; }
        public ICommand NavigateToMembersCommand { get; }
        public ICommand NavigateToReportsCommand { get; }

        private void NavigateToSection(string section)
        {
            SelectedSection = section;
            
            CurrentView = section switch
            {
                "العائلات" => _serviceProvider.GetRequiredService<FamiliesView>(),
                "الأفراد" => _serviceProvider.GetRequiredService<MembersView>(),
                "التقارير" => _serviceProvider.GetRequiredService<ReportsView>(),
                _ => _serviceProvider.GetRequiredService<FamiliesView>()
            };
        }
    }
}
