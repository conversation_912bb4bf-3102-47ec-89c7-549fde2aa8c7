# دليل النشر - نظام الأنساب

## متطلبات النظام

### الحد الأدنى للمتطلبات
- **نظام التشغيل:** Windows 10 (الإصدار 1809) أو أحدث
- **المعالج:** Intel Core i3 أو AMD Ryzen 3 أو أفضل
- **الذاكرة:** 4 GB RAM
- **مساحة القرص:** 500 MB مساحة فارغة
- **.NET Runtime:** .NET 8.0 Desktop Runtime

### المتطلبات الموصى بها
- **نظام التشغيل:** Windows 11
- **المعالج:** Intel Core i5 أو AMD Ryzen 5 أو أفضل
- **الذاكرة:** 8 GB RAM أو أكثر
- **مساحة القرص:** 1 GB مساحة فارغة
- **الشاشة:** دقة 1920x1080 أو أعلى

## إعداد قاعدة البيانات

### 1. تثبيت MySQL Server

#### تحميل MySQL
1. اذهب إلى [موقع MySQL الرسمي](https://dev.mysql.com/downloads/mysql/)
2. حمل MySQL Community Server
3. اختر الإصدار المناسب لنظام التشغيل

#### تثبيت MySQL
1. شغل ملف التثبيت
2. اختر "Developer Default" في نوع التثبيت
3. اتبع خطوات التثبيت
4. اضبط كلمة مرور المستخدم الجذر (root)

### 2. إنشاء قاعدة البيانات

```sql
-- اتصل بـ MySQL كمستخدم جذر
mysql -u root -p

-- أنشئ قاعدة البيانات
CREATE DATABASE FamilyAppDb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- أنشئ مستخدم جديد (اختياري)
CREATE USER 'familyapp'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON FamilyAppDb.* TO 'familyapp'@'localhost';
FLUSH PRIVILEGES;
```

## تكوين التطبيق

### 1. تحديث سلسلة الاتصال

في ملف `App.xaml.cs`، ابحث عن السطر التالي:

```csharp
"Server=localhost;Database=FamilyAppDb;Uid=root;Pwd=;"
```

وقم بتحديثه ليطابق إعدادات قاعدة البيانات الخاصة بك:

```csharp
"Server=localhost;Database=FamilyAppDb;Uid=your_username;Pwd=your_password;"
```

### 2. اختبار الاتصال

قبل تشغيل التطبيق، تأكد من أن:
- خدمة MySQL تعمل
- قاعدة البيانات موجودة
- بيانات الاتصال صحيحة

## تشغيل التطبيق

### من Visual Studio
1. افتح المشروع في Visual Studio
2. اضغط F5 أو اختر "Start Debugging"

### من سطر الأوامر
```bash
cd FamilyApp
dotnet restore
dotnet build
dotnet run
```

### إنشاء ملف تنفيذي
```bash
cd FamilyApp
dotnet publish -c Release -r win-x64 --self-contained true
```

## بيانات الدخول الافتراضية

عند التشغيل الأول، استخدم:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
**الخطأ:** `Unable to connect to any of the specified MySQL hosts`

**الحلول:**
- تأكد من تشغيل خدمة MySQL
- تحقق من سلسلة الاتصال
- تأكد من صحة اسم المستخدم وكلمة المرور

#### 2. خطأ في إنشاء الجداول
**الخطأ:** `Table doesn't exist`

**الحلول:**
- تأكد من وجود قاعدة البيانات
- امنح صلاحيات كاملة للمستخدم
- احذف قاعدة البيانات وأعد إنشاءها

#### 3. مشاكل في الواجهة
**المشكلة:** النصوص لا تظهر بالعربية بشكل صحيح

**الحلول:**
- تأكد من تثبيت خطوط عربية على النظام
- تحقق من إعدادات اللغة في Windows

#### 4. بطء في الأداء
**المشكلة:** التطبيق بطيء في التحميل

**الحلول:**
- تأكد من توفر ذاكرة كافية
- أغلق التطبيقات غير الضرورية
- تحقق من أداء قاعدة البيانات

## النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية
```bash
mysqldump -u root -p FamilyAppDb > backup.sql
```

### استعادة النسخة الاحتياطية
```bash
mysql -u root -p FamilyAppDb < backup.sql
```

## التحديثات المستقبلية

### إضافة ميزات جديدة
- تصدير التقارير إلى PDF
- استيراد البيانات من Excel
- نظام صلاحيات متقدم
- دعم الصور الشخصية

### تحسينات الأداء
- فهرسة قاعدة البيانات
- تحسين الاستعلامات
- ذاكرة التخزين المؤقت

## الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md
3. ابحث في قسم المشاكل المعروفة
4. اتصل بفريق الدعم الفني

## الأمان

### نصائح أمنية مهمة
- غير كلمة مرور المدير الافتراضية
- استخدم كلمات مرور قوية لقاعدة البيانات
- قم بعمل نسخ احتياطية دورية
- حدث التطبيق بانتظام

### صلاحيات قاعدة البيانات
- لا تستخدم المستخدم الجذر للتطبيق
- أنشئ مستخدم مخصص بصلاحيات محدودة
- استخدم اتصال مشفر إذا أمكن
