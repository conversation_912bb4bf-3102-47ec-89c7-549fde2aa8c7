using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public class FamiliesViewModel : BaseViewModel
    {
        private readonly DataService _dataService;
        private ObservableCollection<Family> _families = new();
        private ObservableCollection<Member> _availableMembers = new();
        private Family? _selectedFamily;
        private bool _isLoading = false;
        private string _errorMessage = string.Empty;

        // Form fields
        private string _familyName = string.Empty;
        private Member? _selectedFather;
        private Member? _selectedMother;
        private bool _isEditMode = false;

        public FamiliesViewModel(DataService dataService)
        {
            _dataService = dataService;
            
            // Initialize commands
            LoadFamiliesCommand = new RelayCommand(async () => await LoadFamiliesAsync());
            AddFamilyCommand = new RelayCommand(() => AddFamily());
            EditFamilyCommand = new RelayCommand<Family>(EditFamily);
            DeleteFamilyCommand = new RelayCommand<Family>(async family => await DeleteFamilyAsync(family));
            SaveFamilyCommand = new RelayCommand(async () => await SaveFamilyAsync(), CanSaveFamily);
            CancelEditCommand = new RelayCommand(CancelEdit);
            
            // Load initial data
            _ = Task.Run(async () => await LoadFamiliesAsync());
            _ = Task.Run(async () => await LoadAvailableMembersAsync());
        }

        // Properties
        public ObservableCollection<Family> Families
        {
            get => _families;
            set => SetProperty(ref _families, value);
        }

        public ObservableCollection<Member> AvailableMembers
        {
            get => _availableMembers;
            set => SetProperty(ref _availableMembers, value);
        }

        public Family? SelectedFamily
        {
            get => _selectedFamily;
            set => SetProperty(ref _selectedFamily, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string FamilyName
        {
            get => _familyName;
            set => SetProperty(ref _familyName, value);
        }

        public Member? SelectedFather
        {
            get => _selectedFather;
            set => SetProperty(ref _selectedFather, value);
        }

        public Member? SelectedMother
        {
            get => _selectedMother;
            set => SetProperty(ref _selectedMother, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        // Commands
        public ICommand LoadFamiliesCommand { get; }
        public ICommand AddFamilyCommand { get; }
        public ICommand EditFamilyCommand { get; }
        public ICommand DeleteFamilyCommand { get; }
        public ICommand SaveFamilyCommand { get; }
        public ICommand CancelEditCommand { get; }

        // Methods
        private async Task LoadFamiliesAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                
                var families = await _dataService.GetAllFamiliesAsync();
                Families = new ObservableCollection<Family>(families);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل العائلات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadAvailableMembersAsync()
        {
            try
            {
                var members = await _dataService.GetAllMembersAsync();

                // Ensure we update on UI thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    AvailableMembers.Clear();
                    foreach (var member in members)
                    {
                        AvailableMembers.Add(member);
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل الأعضاء: {ex.Message}";
            }
        }

        private void AddFamily()
        {
            ClearForm();
            IsEditMode = true;
        }

        private void EditFamily(Family? family)
        {
            if (family == null) return;
            
            SelectedFamily = family;
            FamilyName = family.FamilyName;
            SelectedFather = family.Father;
            SelectedMother = family.Mother;
            IsEditMode = true;
        }

        private async Task SaveFamilyAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                if (SelectedFamily == null)
                {
                    // Create new family
                    var newFamily = new Family
                    {
                        FamilyName = FamilyName,
                        FatherId = SelectedFather?.Id,
                        MotherId = SelectedMother?.Id
                    };
                    
                    await _dataService.CreateFamilyAsync(newFamily);
                }
                else
                {
                    // Update existing family
                    SelectedFamily.FamilyName = FamilyName;
                    SelectedFamily.FatherId = SelectedFather?.Id;
                    SelectedFamily.MotherId = SelectedMother?.Id;
                    
                    await _dataService.UpdateFamilyAsync(SelectedFamily);
                }

                await LoadFamiliesAsync();
                CancelEdit();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ العائلة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task DeleteFamilyAsync(Family? family)
        {
            if (family == null) return;

            try
            {
                IsLoading = true;
                await _dataService.DeleteFamilyAsync(family.Id);
                await LoadFamiliesAsync();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حذف العائلة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void CancelEdit()
        {
            IsEditMode = false;
            ClearForm();
        }

        private void ClearForm()
        {
            SelectedFamily = null;
            FamilyName = string.Empty;
            SelectedFather = null;
            SelectedMother = null;
        }

        private bool CanSaveFamily()
        {
            return !string.IsNullOrWhiteSpace(FamilyName) && !IsLoading;
        }
    }
}
