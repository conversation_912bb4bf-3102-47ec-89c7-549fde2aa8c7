using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public partial class FamiliesViewModel : BaseViewModel
    {
        private readonly DataService _dataService;

        [ObservableProperty]
        private ObservableCollection<Family> families = new();

        [ObservableProperty]
        private ObservableCollection<Member> availableFathers = new();

        [ObservableProperty]
        private ObservableCollection<Member> availableMothers = new();

        [ObservableProperty]
        private Family? selectedFamily;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        // Form fields
        [ObservableProperty]
        private string familyName = string.Empty;

        [ObservableProperty]
        private Member? selectedFather;

        [ObservableProperty]
        private Member? selectedMother;

        [ObservableProperty]
        private bool isEditMode = false;
        private bool _isEditMode = false;

        public FamiliesViewModel(DataService dataService)
        {
            _dataService = dataService;
            
            // Load initial data
            LoadDataAsync();
            
            // Load initial data
            LoadDataAsync();
        }

        // Events
        public event EventHandler? AddFamilyRequested;

        [RelayCommand]
        private async Task RefreshAsync()
        {
            await LoadFamiliesAsync();
            await LoadAvailableMembersAsync();
        }

        [RelayCommand]
        private void AddFamily()
        {
            AddFamilyRequested?.Invoke(this, EventArgs.Empty);
        }

        [RelayCommand]
        private async Task EditFamilyAsync(Family? family)
        {
            if (family == null) return;

            try
            {
                // For now, show a message that edit functionality will be available soon
                ErrorMessage = "ميزة تعديل العائلة ستكون متاحة قريباً";
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تعديل العائلة: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task DeleteFamilyAsync(Family? family)
        {
            if (family == null) return;

            try
            {
                // Show confirmation dialog
                var result = System.Windows.MessageBox.Show(
                    $"هل أنت متأكد من حذف العائلة '{family.FamilyName}'؟\nسيتم حذف جميع الأعضاء المرتبطين بهذه العائلة.",
                    "تأكيد الحذف",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    await _dataService.DeleteFamilyAsync(family.Id);
                    await LoadFamiliesAsync();
                    ErrorMessage = $"تم حذف العائلة '{family.FamilyName}' بنجاح";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حذف العائلة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Methods
        private async void LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                await LoadFamiliesAsync();
                await Task.Delay(100); // Small delay to avoid threading issues
                await LoadAvailableMembersAsync();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadFamiliesAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                
                var families = await _dataService.GetAllFamiliesAsync();
                Families = new ObservableCollection<Family>(families);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل العائلات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadAvailableMembersAsync()
        {
            try
            {
                var members = await _dataService.GetAllMembersAsync();
                var fathers = members.Where(x => x.Gender == Gender.Male).ToList();
                var mothers = members.Where(x => x.Gender == Gender.Female).ToList();
                
                AvailableFathers = new ObservableCollection<Member>(fathers);
                AvailableMothers = new ObservableCollection<Member>(mothers);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل الأعضاء: {ex.Message}";
            }
        }


    }
}
