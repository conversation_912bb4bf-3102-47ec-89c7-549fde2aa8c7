using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public partial class FamiliesViewModel : BaseViewModel
    {
        private readonly DataService _dataService;

        [ObservableProperty]
        private ObservableCollection<Family> families = new();

        [ObservableProperty]
        private ObservableCollection<Member> availableFathers = new();

        [ObservableProperty]
        private ObservableCollection<Member> availableMothers = new();

        [ObservableProperty]
        private Family? selectedFamily;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        // Form fields
        [ObservableProperty]
        private string familyName = string.Empty;

        [ObservableProperty]
        private Member? selectedFather;

        [ObservableProperty]
        private Member? selectedMother;

        [ObservableProperty]
        private bool isEditMode = false;
        private bool _isEditMode = false;

        public FamiliesViewModel(DataService dataService)
        {
            _dataService = dataService;
            
            // Load initial data
            LoadDataAsync();
            
            // Load initial data
            LoadDataAsync();
        }

        // Events
        public event EventHandler? AddFamilyRequested;

        [RelayCommand]
        private async Task RefreshAsync()
        {
            await LoadFamiliesAsync();
            await LoadAvailableMembersAsync();
        }

        [RelayCommand]
        private void AddFamily()
        {
            AddFamilyRequested?.Invoke(this, EventArgs.Empty);
        }

        // Methods
        private async void LoadDataAsync()
        {
            await LoadFamiliesAsync();
            await LoadAvailableMembersAsync();
        }

        private async Task LoadFamiliesAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;
                
                var families = await _dataService.GetAllFamiliesAsync();
                Families = new ObservableCollection<Family>(families);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل العائلات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadAvailableMembersAsync()
        {
            try
            {
                var members = await _dataService.GetAllMembersAsync();
                var fathers = members.Where(x => x.Gender == Gender.Male).ToList();
                var mothers = members.Where(x => x.Gender == Gender.Female).ToList();
                
                AvailableFathers = new ObservableCollection<Member>(fathers);
                AvailableMothers = new ObservableCollection<Member>(mothers);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل الأعضاء: {ex.Message}";
            }
        }


    }
}
