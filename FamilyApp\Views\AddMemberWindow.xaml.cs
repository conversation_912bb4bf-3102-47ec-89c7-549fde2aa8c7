using System.Windows;
using Microsoft.Win32;
using FamilyApp.ViewModels;

namespace FamilyApp.Views
{
    public partial class AddMemberWindow : Window
    {
        public AddMemberWindow(AddMemberViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to close event
            if (viewModel != null)
            {
                viewModel.CloseRequested += (sender, success) =>
                {
                    DialogResult = success;
                    Close();
                };
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void SelectPicture_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر صورة",
                Filter = "Image files (*.jpg, *.jpeg, *.png, *.bmp)|*.jpg;*.jpeg;*.png;*.bmp|All files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                if (DataContext is AddMemberViewModel viewModel)
                {
                    viewModel.PicturePath = openFileDialog.FileName;
                }
            }
        }
    }
}
