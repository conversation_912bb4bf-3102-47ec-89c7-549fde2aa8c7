﻿<Application x:Class="FamilyApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:FamilyApp.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <local:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
            <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <local:BooleanAndConverter x:Key="BooleanAndConverter"/>
            <local:FamilyFormTitleConverter x:Key="FamilyFormTitleConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
