﻿<Application x:Class="FamilyApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:FamilyApp.Converters"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign3.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Styles for Arabic Support -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>

            <Style TargetType="{x:Type UserControl}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="FontSize" Value="14"/>
            </Style>

            <Style TargetType="{x:Type TextBlock}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="TextAlignment" Value="Right"/>
            </Style>

            <Style TargetType="{x:Type TextBox}" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>

            <Style TargetType="{x:Type ComboBox}" BasedOn="{StaticResource MaterialDesignComboBox}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>

            <Style TargetType="{x:Type Button}" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
            </Style>

            <Style TargetType="{x:Type DataGrid}" BasedOn="{StaticResource MaterialDesignDataGrid}">
                <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>

            <!-- Converters -->
            <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <local:StringToBooleanConverter x:Key="StringToBooleanConverter"/>
            <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
            <local:BooleanAndConverter x:Key="BooleanAndConverter"/>
            <local:FamilyFormTitleConverter x:Key="FamilyFormTitleConverter"/>
            <local:MemberFormTitleConverter x:Key="MemberFormTitleConverter"/>
            <local:GenderToStringConverter x:Key="GenderToStringConverter"/>
            <local:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
