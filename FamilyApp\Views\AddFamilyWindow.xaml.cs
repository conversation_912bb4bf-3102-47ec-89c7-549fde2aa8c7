using System.Windows;
using FamilyApp.ViewModels;

namespace FamilyApp.Views
{
    public partial class AddFamilyWindow : Window
    {
        public AddFamilyWindow(AddFamilyViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to close event
            if (viewModel != null)
            {
                viewModel.CloseRequested += (sender, success) =>
                {
                    DialogResult = success;
                    Close();
                };
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
