using System.Windows;
using FamilyApp.ViewModels;

namespace FamilyApp.Views
{
    public partial class ImageViewerWindow : Window
    {
        public ImageViewerWindow(ImageViewerViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
            
            // Subscribe to close event
            if (viewModel != null)
            {
                viewModel.CloseRequested += (sender, e) =>
                {
                    Close();
                };
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
