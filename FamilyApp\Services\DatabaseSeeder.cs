using FamilyApp.Database;
using FamilyApp.Models;

namespace FamilyApp.Services
{
    public class DatabaseSeeder
    {
        private readonly AppDbContext _context;

        public DatabaseSeeder(AppDbContext context)
        {
            _context = context;
        }

        public async Task SeedAsync()
        {
            // Check if data already exists
            if (_context.Members.Any() || _context.Families.Any())
                return;

            // Create sample members
            var members = new List<Member>
            {
                new Member { FullName = "أحمد محمد علي", Gender = Gender.Male, DateOfBirth = new DateTime(1950, 1, 15), DateOfDeath = new DateTime(2020, 5, 10), GraveNumber = "A-123", GravePlace = "مقبرة السلام" },
                new Member { FullName = "فاطمة أحمد حسن", Gender = Gender.Female, DateOfBirth = new DateTime(1955, 3, 20) },
                new Member { FullName = "محمد أحمد علي", Gender = Gender.Male, DateOfBirth = new DateTime(1980, 5, 10) },
                new Member { FullName = "عائشة سالم محمد", Gender = Gender.Female, DateOfBirth = new DateTime(1985, 7, 25) },
                new Member { FullName = "علي محمد أحمد", Gender = Gender.Male, DateOfBirth = new DateTime(2010, 9, 12) },
                new Member { FullName = "مريم محمد أحمد", Gender = Gender.Female, DateOfBirth = new DateTime(2012, 11, 8) },
                new Member { FullName = "سالم عبدالله أحمد", Gender = Gender.Male, DateOfBirth = new DateTime(1945, 2, 28), DateOfDeath = new DateTime(2018, 12, 3), GraveNumber = "B-456", GravePlace = "مقبرة النور" },
                new Member { FullName = "خديجة محمد حسن", Gender = Gender.Female, DateOfBirth = new DateTime(1950, 6, 14) },
                new Member { FullName = "عبدالله سالم عبدالله", Gender = Gender.Male, DateOfBirth = new DateTime(1975, 4, 18) },
                new Member { FullName = "زينب أحمد محمد", Gender = Gender.Female, DateOfBirth = new DateTime(1978, 8, 22) },
                new Member { FullName = "يوسف علي أحمد", Gender = Gender.Male, DateOfBirth = new DateTime(1990, 1, 5) },
                new Member { FullName = "نور محمد سالم", Gender = Gender.Female, DateOfBirth = new DateTime(1992, 8, 15) }
            };

            _context.Members.AddRange(members);
            await _context.SaveChangesAsync();

            // Create sample families
            var families = new List<Family>
            {
                new Family 
                { 
                    FamilyName = "عائلة أحمد علي",
                    FatherId = members[0].Id, // أحمد محمد علي
                    MotherId = members[1].Id  // فاطمة أحمد حسن
                },
                new Family 
                { 
                    FamilyName = "عائلة محمد أحمد",
                    FatherId = members[2].Id, // محمد أحمد علي
                    MotherId = members[3].Id  // عائشة سالم محمد
                },
                new Family 
                { 
                    FamilyName = "عائلة سالم عبدالله",
                    FatherId = members[6].Id, // سالم عبدالله أحمد
                    MotherId = members[7].Id  // خديجة محمد حسن
                }
            };

            _context.Families.AddRange(families);
            await _context.SaveChangesAsync();

            // Update members with family assignments
            members[2].FamilyId = families[0].Id; // محمد ابن أحمد
            members[4].FamilyId = families[1].Id; // علي ابن محمد
            members[5].FamilyId = families[1].Id; // مريم ابنة محمد
            members[8].FamilyId = families[2].Id; // عبدالله ابن سالم
            members[9].FamilyId = families[2].Id; // زينب ابنة سالم

            await _context.SaveChangesAsync();
        }
    }
}
