<Window x:Class="FamilyApp.Views.AddMemberWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة فرد جديد" 
        Height="700" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <materialDesign:Card Margin="20" Padding="30">
            <StackPanel>
                <!-- Header -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,30">
                    <TextBlock Text="👤" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة فرد جديد" 
                              FontSize="20" 
                              FontWeight="Bold"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Full Name -->
                <TextBox materialDesign:HintAssist.Hint="الاسم الكامل *"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding FullName, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"
                        FontSize="14"/>

                <!-- Gender Selection -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="النوع *" Margin="0,0,0,10" FontWeight="Bold"/>
                    <StackPanel Orientation="Horizontal">
                        <RadioButton Content="ذكر" 
                                   IsChecked="{Binding IsMale}"
                                   Margin="0,0,30,0"
                                   FontSize="14"/>
                        <RadioButton Content="أنثى" 
                                   IsChecked="{Binding IsFemale}"
                                   FontSize="14"/>
                    </StackPanel>
                </StackPanel>

                <!-- Date of Birth -->
                <DatePicker materialDesign:HintAssist.Hint="تاريخ الميلاد"
                           materialDesign:HintAssist.IsFloating="True"
                           SelectedDate="{Binding DateOfBirth}"
                           Margin="0,0,0,20"
                           FontSize="14"/>

                <!-- Date of Death -->
                <DatePicker materialDesign:HintAssist.Hint="تاريخ الوفاة (اختياري)"
                           materialDesign:HintAssist.IsFloating="True"
                           SelectedDate="{Binding DateOfDeath}"
                           Margin="0,0,0,20"
                           FontSize="14"/>

                <!-- Grave Number -->
                <TextBox materialDesign:HintAssist.Hint="رقم القبر (اختياري)"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding GraveNumber, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"
                        FontSize="14"/>

                <!-- Grave Place -->
                <TextBox materialDesign:HintAssist.Hint="مكان القبر (اختياري)"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding GravePlace, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"
                        FontSize="14"/>

                <!-- Picture Path -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                    <TextBox materialDesign:HintAssist.Hint="مسار الصورة (اختياري)"
                            materialDesign:HintAssist.IsFloating="True"
                            Text="{Binding PicturePath, UpdateSourceTrigger=PropertyChanged}"
                            Width="300"
                            Margin="0,0,10,0"
                            FontSize="14"/>
                    <Button Content="اختر صورة" 
                           Click="SelectPicture_Click"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           FontSize="12"/>
                </StackPanel>

                <!-- Family Selection -->
                <ComboBox materialDesign:HintAssist.Hint="العائلة (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableFamilies}"
                         SelectedItem="{Binding SelectedFamily}"
                         DisplayMemberPath="FamilyName"
                         Margin="0,0,0,30"
                         FontSize="14"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                          Foreground="Red"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,20"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Content="حفظ"
                           Command="{Binding SaveCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Width="100"
                           Height="40"
                           Margin="0,0,15,0"
                           FontSize="14"/>
                    
                    <Button Content="إلغاء"
                           Click="Cancel_Click"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Width="100"
                           Height="40"
                           FontSize="14"/>
                </StackPanel>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,20,0,0"
                            Height="4"/>
            </StackPanel>
        </materialDesign:Card>
    </ScrollViewer>
</Window>
