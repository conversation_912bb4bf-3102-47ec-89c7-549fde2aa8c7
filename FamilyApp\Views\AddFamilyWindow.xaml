<Window x:Class="FamilyApp.Views.AddFamilyWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة عائلة جديدة" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <materialDesign:Card Margin="20" Padding="30">
            <StackPanel>
                <!-- Header -->
                <StackPanel Orientation="Horizontal" Margin="0,0,0,30">
                    <TextBlock Text="👨‍👩‍👧‍👦" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة عائلة جديدة" 
                              FontSize="20" 
                              FontWeight="Bold"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Family Name -->
                <TextBox materialDesign:HintAssist.Hint="اسم العائلة *"
                        materialDesign:HintAssist.IsFloating="True"
                        Text="{Binding FamilyName, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,0,20"
                        FontSize="14"/>

                <!-- Father Selection -->
                <ComboBox materialDesign:HintAssist.Hint="الأب (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableFathers}"
                         SelectedItem="{Binding SelectedFather}"
                         DisplayMemberPath="FullName"
                         Margin="0,0,0,20"
                         FontSize="14"/>

                <!-- Mother Selection -->
                <ComboBox materialDesign:HintAssist.Hint="الأم (اختياري)"
                         materialDesign:HintAssist.IsFloating="True"
                         ItemsSource="{Binding AvailableMothers}"
                         SelectedItem="{Binding SelectedMother}"
                         DisplayMemberPath="FullName"
                         Margin="0,0,0,30"
                         FontSize="14"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}"
                          Foreground="Red"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,20"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Content="حفظ"
                           Command="{Binding SaveCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Width="100"
                           Height="40"
                           Margin="0,0,15,0"
                           FontSize="14"/>
                    
                    <Button Content="إلغاء"
                           Click="Cancel_Click"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Width="100"
                           Height="40"
                           FontSize="14"/>
                </StackPanel>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            Margin="0,20,0,0"
                            Height="4"/>

                <!-- Instructions -->
                <Border Background="{DynamicResource MaterialDesignDivider}"
                       Padding="15"
                       Margin="0,30,0,0"
                       CornerRadius="4">
                    <StackPanel>
                        <TextBlock Text="تعليمات:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• اسم العائلة مطلوب" FontSize="11" Margin="0,0,0,2"/>
                        <TextBlock Text="• يمكن اختيار الأب والأم من القائمة أو تركهما فارغين" FontSize="11" Margin="0,0,0,2"/>
                        <TextBlock Text="• يمكن إضافة الأب والأم لاحقاً من قسم الأفراد" FontSize="11"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </materialDesign:Card>
    </ScrollViewer>
</Window>
