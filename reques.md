
---

### 📌 **Project Title**: Family Generation System (نظام الأنساب)

---

## 🧾 **Overview**

We are requesting the development of a desktop application for managing family trees (نظام الأنساب العائلية) using **WPF**, **C#**, and **MySQL**. The application must follow **MVVM (Model-View-ViewModel)** architecture, support a **modern and elegant Arabic-only UI**, and offer **CRUD** operations for families and their members.

---

## 🧩 **Technologies Required**

* **Frontend**: WPF (.NET 8), XAML
* **Architecture**: MVVM Pattern
* **Backend**: C#
* **Database**: MySQL
* **ORM**: Entity Framework Core (with MySQL provider)
* **UI Libraries** (optional but recommended): MahApps.Metro / MaterialDesignInXAML / FluentWPF
* **Localization**: Arabic only, full RTL layout and flow direction

---

## 🔐 **Authentication**

### 🧑‍💼 **Login Page**

* Form with:

  * Username (اسم المستخدم)
  * Password (كلمة المرور)
* Validate against Users table in the database
* "تسجيل الدخول" button
* Remember Me checkbox (اختياري)

---

## 🏠 **Main Window (Home)**

* Fixed **Sidebar Menu** (RTL-aligned) with icons and Arabic labels:

  * 👨‍👩‍👧‍👦 العائلات (Families)
  * 👤 الأفراد (Members)
  * 📊 التقارير (Reports)
* Main content area changes based on sidebar selection.
* Status bar (optional): Logged in user, date/time.

---

## 👨‍👩‍👧‍👦 **Families Section (العائلات)**

### Entity: `Family`

* Fields:

  * Family ID (معرّف العائلة)
  * Family Name (اسم العائلة)
  * Father (الأب) → linked to Member entity ( may create a new member if not exists)
  * Mother (الأم) → linked to Member entity ( may create a new member if not exists)
  * Members (list of linked members)
* Operations:

  * Create new family
  * Assign existing members as father/mother
  * View all families in a DataGrid
  * Edit/Delete family
* UI:

  * Form for entering family name, selecting father and mother from member list
  * Grid view of all families with options for edit/delete
  * Modal dialog or side drawer for adding/editing

---

## 👤 **Members Section (الأفراد)**

### Entity: `Member`

* Fields:

  * Member ID (معرّف الفرد)
  * Full Name (الاسم الكامل)
  * Gender (النوع) → ذكر / أنثى
  * Date of Birth (تاريخ الميلاد)
  * Belongs to Family (تنتمي إلى عائلة) → foreign key to Family
* Operations:

  * Add member
  * Edit/Delete member
  * Assign to existing family
* UI:

  * Input form in RTL with validation
  * Gender selection with toggle buttons or radio
  * Family assignment using dropdown
  * DataGrid for browsing/filtering/searching members

---

## 📊 **Reports Section (التقارير)**

Generate family lineage reports:

### 1. **عرض شجرة العائلة (Family Tree Report)**

* Input: Select a Family
* Output: Tree view of father, mother, and all descendants

### 2. **جذر الفرد (Find Root of Member)**

* Input: Select a member
* Output: Find the top ancestor (oldest parent in lineage)

### 3. **أحفاد الفرد (Find Grandsons of Member)**

* Input: Select a member
* Output: All direct and indirect grandsons (children of children)

#### UI:

* Tree view or expandable list
* Ability to export to PDF or print
* Arabic labels for nodes (اسم الفرد)

---

## 🎨 **UI/UX Requirements**

* **Modern, clean interface** (Material or Fluent UI design)
* **Arabic only** (لا يدعم اللغة الإنجليزية):

  * Full RTL layout (FlowDirection="RightToLeft")
  * Arabic font (e.g., Cairo, Noto Kufi Arabic)
  * Use of appropriate icons for Arabic context
* Form validations with clear error messages in Arabic
* Smooth transitions and dialogs

---

## 📁 **Folder Structure**

```bash
FamilyApp/
│
├── Models/
│   ├── Family.cs
│   └── Member.cs
│
├── ViewModels/
│   ├── LoginViewModel.cs
│   ├── HomeViewModel.cs
│   ├── FamiliesViewModel.cs
│   ├── MembersViewModel.cs
│   └── ReportsViewModel.cs
│
├── Views/
│   ├── LoginPage.xaml
│   ├── Home.xaml
│   ├── FamiliesView.xaml
│   ├── MembersView.xaml
│   └── ReportsView.xaml
│
├── Services/
│   ├── DataService.cs
│   └── ReportService.cs
│
├── Database/
│   └── AppDbContext.cs
│
└── App.xaml.cs
```

---

## 🗄️ **Database Structure**

### Table: `Users`

| Id | Username | PasswordHash |
| -- | -------- | ------------ |

### Table: `Members`

\| Id | FullName | Gender | DOB | FamilyId (FK) |

### Table: `Families`

\| Id | FamilyName | FatherId (FK→Members) | MotherId (FK→Members) |

---

## ✅ **Acceptance Criteria**

* MVVM architecture respected (no logic in code-behind)
* Functional and responsive UI
* All CRUD operations work correctly with MySQL
* Arabic-only RTL support throughout
* Clean, maintainable, and well-documented codebase
* Tree-based reports work without errors
* Can run with a sample MySQL database

---

## 🔚 **Optional Features**

* User registration with roles (admin/viewer)
* Export reports as PDF or image
* Search/filter by name, gender, or date of birth

---