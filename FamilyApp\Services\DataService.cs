using Microsoft.EntityFrameworkCore;
using FamilyApp.Database;
using FamilyApp.Models;

namespace FamilyApp.Services
{
    public class DataService
    {
        private readonly AppDbContext _context;

        public DataService(AppDbContext context)
        {
            _context = context;
        }

        // User methods
        public async Task<User?> AuthenticateUserAsync(string username, string password)
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Username == username);
            if (user != null && BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
            {
                return user;
            }
            return null;
        }

        // Family methods
        public async Task<List<Family>> GetAllFamiliesAsync()
        {
            return await _context.Families
                .Include(f => f.Father)
                .Include(f => f.Mother)
                .Include(f => f.Members)
                .ToListAsync();
        }

        public async Task<Family?> GetFamilyByIdAsync(int id)
        {
            return await _context.Families
                .Include(f => f.Father)
                .Include(f => f.Mother)
                .Include(f => f.Members)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<Family> CreateFamilyAsync(Family family)
        {
            _context.Families.Add(family);
            await _context.SaveChangesAsync();
            return family;
        }

        public async Task<Family> UpdateFamilyAsync(Family family)
        {
            _context.Families.Update(family);
            await _context.SaveChangesAsync();
            return family;
        }

        public async Task DeleteFamilyAsync(int id)
        {
            var family = await _context.Families.FindAsync(id);
            if (family != null)
            {
                _context.Families.Remove(family);
                await _context.SaveChangesAsync();
            }
        }

        // Member methods
        public async Task<List<Member>> GetAllMembersAsync()
        {
            return await _context.Members
                .Include(m => m.Family)
                .ToListAsync();
        }

        public async Task<Member?> GetMemberByIdAsync(int id)
        {
            return await _context.Members
                .Include(m => m.Family)
                .FirstOrDefaultAsync(m => m.Id == id);
        }

        public async Task<Member> CreateMemberAsync(Member member)
        {
            _context.Members.Add(member);
            await _context.SaveChangesAsync();
            return member;
        }

        public async Task<Member> UpdateMemberAsync(Member member)
        {
            _context.Members.Update(member);
            await _context.SaveChangesAsync();
            return member;
        }

        public async Task DeleteMemberAsync(int id)
        {
            var member = await _context.Members.FindAsync(id);
            if (member != null)
            {
                _context.Members.Remove(member);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<List<Member>> GetMembersWithoutFamilyAsync()
        {
            return await _context.Members
                .Where(m => m.FamilyId == null)
                .ToListAsync();
        }

        public async Task<List<Member>> SearchMembersAsync(string searchTerm)
        {
            return await _context.Members
                .Include(m => m.Family)
                .Where(m => m.FullName.Contains(searchTerm))
                .ToListAsync();
        }
    }
}
