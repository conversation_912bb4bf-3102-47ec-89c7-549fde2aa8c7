using System.Windows;
using System.Windows.Input;
using FamilyApp.Services;
using FamilyApp.Views;
using Microsoft.Extensions.DependencyInjection;

namespace FamilyApp.ViewModels
{
    public class LoginViewModel : BaseViewModel
    {
        private readonly DataService _dataService;
        private readonly IServiceProvider _serviceProvider;
        private string _username = string.Empty;
        private string _password = string.Empty;
        private string _errorMessage = string.Empty;
        private bool _isLoading = false;

        public LoginViewModel(DataService dataService, IServiceProvider serviceProvider)
        {
            _dataService = dataService;
            _serviceProvider = serviceProvider;
            LoginCommand = new RelayCommand(async () => await LoginAsync(), CanLogin);
        }

        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value);
        }

        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public ICommand LoginCommand { get; }

        private bool CanLogin()
        {
            return !string.IsNullOrWhiteSpace(Username) && 
                   !string.IsNullOrWhiteSpace(Password) && 
                   !IsLoading;
        }

        private async Task LoginAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var user = await _dataService.AuthenticateUserAsync(Username, Password);
                
                if (user != null)
                {
                    // Login successful, open main window
                    var mainWindow = _serviceProvider.GetRequiredService<Home>();
                    mainWindow.Show();
                    
                    // Close login window
                    Application.Current.Windows.OfType<LoginPage>().FirstOrDefault()?.Close();
                }
                else
                {
                    ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في الاتصال: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
}
