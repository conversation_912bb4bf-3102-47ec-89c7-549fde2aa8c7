# نظام الأنساب (Family Generation System)

## نظرة عامة

نظام الأنساب هو تطبيق سطح مكتب مطور باستخدام WPF و .NET 8 لإدارة شجرة العائلة والأنساب. يدعم التطبيق اللغة العربية بالكامل مع تخطيط RTL ويتبع نمط MVVM.

## المتطلبات التقنية

- **.NET 8** أو أحدث
- **MySQL Server** (الإصدار 8.0 أو أحدث)
- **Windows 10/11**

## الحزم المستخدمة

- `Microsoft.EntityFrameworkCore` - للتعامل مع قاعدة البيانات
- `Pomelo.EntityFrameworkCore.MySql` - موفر MySQL
- `MaterialDesignThemes` - للواجهة الحديثة
- `BCrypt.Net-Next` - لتشفير كلمات المرور
- `Microsoft.Extensions.Hosting` - لحقن التبعيات

## هيكل المشروع

```
FamilyApp/
├── Models/           # نماذج البيانات
│   ├── User.cs
│   ├── Family.cs
│   └── Member.cs
├── ViewModels/       # نماذج العرض
│   ├── BaseViewModel.cs
│   ├── LoginViewModel.cs
│   ├── HomeViewModel.cs
│   ├── FamiliesViewModel.cs
│   ├── MembersViewModel.cs
│   └── ReportsViewModel.cs
├── Views/            # واجهات المستخدم
│   ├── LoginPage.xaml
│   ├── Home.xaml
│   ├── FamiliesView.xaml
│   ├── MembersView.xaml
│   └── ReportsView.xaml
├── Services/         # الخدمات
│   ├── DataService.cs
│   └── ReportService.cs
├── Database/         # سياق قاعدة البيانات
│   └── AppDbContext.cs
├── Converters/       # محولات البيانات
│   └── BooleanConverters.cs
└── App.xaml         # إعدادات التطبيق
```

## قاعدة البيانات

### جداول قاعدة البيانات

1. **Users** - المستخدمين
   - Id (مفتاح أساسي)
   - Username (اسم المستخدم)
   - PasswordHash (كلمة المرور المشفرة)

2. **Families** - العائلات
   - Id (مفتاح أساسي)
   - FamilyName (اسم العائلة)
   - FatherId (مفتاح خارجي للأب)
   - MotherId (مفتاح خارجي للأم)

3. **Members** - الأعضاء
   - Id (مفتاح أساسي)
   - FullName (الاسم الكامل)
   - Gender (النوع)
   - DateOfBirth (تاريخ الميلاد)
   - FamilyId (مفتاح خارجي للعائلة)

## إعداد التطبيق

### 1. إعداد قاعدة البيانات

```sql
CREATE DATABASE FamilyAppDb;
```

### 2. تحديث سلسلة الاتصال

في ملف `App.xaml.cs`، قم بتحديث سلسلة الاتصال:

```csharp
"Server=localhost;Database=FamilyAppDb;Uid=root;Pwd=your_password;"
```

### 3. تشغيل التطبيق

```bash
cd FamilyApp
dotnet restore
dotnet build
dotnet run
```

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## الميزات الرئيسية

### 1. إدارة العائلات
- إضافة عائلة جديدة
- تعديل بيانات العائلة
- حذف العائلة
- ربط الأب والأم بالعائلة

### 2. إدارة الأعضاء
- إضافة عضو جديد
- تعديل بيانات العضو
- حذف العضو
- البحث في الأعضاء
- ربط العضو بعائلة

### 3. التقارير
- **شجرة العائلة:** عرض شجرة كاملة للعائلة
- **جذر الفرد:** العثور على الجد الأكبر للفرد
- **أحفاد الفرد:** عرض جميع أحفاد الفرد

## الواجهة

- **تصميم Material Design** حديث وأنيق
- **دعم كامل للغة العربية** مع تخطيط RTL
- **شريط جانبي للتنقل** بين الأقسام المختلفة
- **نماذج تفاعلية** لإدخال البيانات
- **جداول بيانات** لعرض المعلومات

## الأمان

- تشفير كلمات المرور باستخدام BCrypt
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection عبر Entity Framework

## المساهمة

لتطوير التطبيق:

1. استنسخ المشروع
2. أنشئ فرع جديد للميزة
3. اختبر التغييرات
4. أرسل طلب دمج

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.
