using System.Collections.ObjectModel;
using System.Windows.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public class FamilyTreeNodeViewModel : BaseViewModel
    {
        public Member Member { get; set; } = new();
        public ObservableCollection<FamilyTreeNodeViewModel> Children { get; set; } = new();
        public int Level { get; set; }
        public string DisplayName => Member.FullName;
        public string GenderDisplay => Member.Gender == Gender.Male ? "ذكر" : "أنثى";
        public string DateOfBirthDisplay => Member.DateOfBirth?.ToString("dd/MM/yyyy") ?? "غير محدد";
    }

    public class ReportsViewModel : BaseViewModel
    {
        private readonly DataService _dataService;
        private readonly ReportService _reportService;
        private ObservableCollection<Family> _families = new();
        private ObservableCollection<Member> _members = new();
        private Family? _selectedFamily;
        private Member? _selectedMember;
        private ObservableCollection<FamilyTreeNodeViewModel> _familyTree = new();
        private ObservableCollection<Member> _descendants = new();
        private Member? _rootAncestor;
        private bool _isLoading = false;
        private string _errorMessage = string.Empty;
        private string _selectedReportType = "شجرة العائلة";

        public ReportsViewModel(DataService dataService, ReportService reportService)
        {
            _dataService = dataService;
            _reportService = reportService;

            // Initialize commands
            LoadDataCommand = new RelayCommand(async () => await LoadDataAsync());
            GenerateFamilyTreeCommand = new RelayCommand(async () => await GenerateFamilyTreeAsync(), CanGenerateFamilyTree);
            FindRootAncestorCommand = new RelayCommand(async () => await FindRootAncestorAsync(), CanFindRootAncestor);
            FindDescendantsCommand = new RelayCommand(async () => await FindDescendantsAsync(), CanFindDescendants);

            // Load initial data
            _ = Task.Run(async () => await LoadDataAsync());
        }

        // Properties
        public ObservableCollection<Family> Families
        {
            get => _families;
            set => SetProperty(ref _families, value);
        }

        public ObservableCollection<Member> Members
        {
            get => _members;
            set => SetProperty(ref _members, value);
        }

        public Family? SelectedFamily
        {
            get => _selectedFamily;
            set => SetProperty(ref _selectedFamily, value);
        }

        public Member? SelectedMember
        {
            get => _selectedMember;
            set => SetProperty(ref _selectedMember, value);
        }

        public ObservableCollection<FamilyTreeNodeViewModel> FamilyTree
        {
            get => _familyTree;
            set => SetProperty(ref _familyTree, value);
        }

        public ObservableCollection<Member> Descendants
        {
            get => _descendants;
            set => SetProperty(ref _descendants, value);
        }

        public Member? RootAncestor
        {
            get => _rootAncestor;
            set => SetProperty(ref _rootAncestor, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        public string SelectedReportType
        {
            get => _selectedReportType;
            set => SetProperty(ref _selectedReportType, value);
        }

        public string[] ReportTypes => new[] { "شجرة العائلة", "جذر الفرد", "أحفاد الفرد" };

        // Commands
        public ICommand LoadDataCommand { get; }
        public ICommand GenerateFamilyTreeCommand { get; }
        public ICommand FindRootAncestorCommand { get; }
        public ICommand FindDescendantsCommand { get; }

        // Methods
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var families = await _dataService.GetAllFamiliesAsync();
                var members = await _dataService.GetAllMembersAsync();

                Families = new ObservableCollection<Family>(families);
                Members = new ObservableCollection<Member>(members);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task GenerateFamilyTreeAsync()
        {
            if (SelectedFamily == null) return;

            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var treeRoot = await _reportService.GenerateFamilyTreeAsync(SelectedFamily.Id);

                if (treeRoot != null)
                {
                    var treeViewModel = ConvertToViewModel(treeRoot);
                    FamilyTree = new ObservableCollection<FamilyTreeNodeViewModel> { treeViewModel };
                }
                else
                {
                    FamilyTree.Clear();
                    ErrorMessage = "لا توجد بيانات شجرة عائلة لهذه العائلة";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في إنشاء شجرة العائلة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task FindRootAncestorAsync()
        {
            if (SelectedMember == null) return;

            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var root = await _reportService.FindRootAncestorAsync(SelectedMember.Id);
                RootAncestor = root;

                if (root == null)
                {
                    ErrorMessage = "لم يتم العثور على جذر للفرد المحدد";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في البحث عن الجذر: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task FindDescendantsAsync()
        {
            if (SelectedMember == null) return;

            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var descendants = await _reportService.FindDescendantsAsync(SelectedMember.Id);
                Descendants = new ObservableCollection<Member>(descendants);

                if (!descendants.Any())
                {
                    ErrorMessage = "لا توجد أحفاد للفرد المحدد";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في البحث عن الأحفاد: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private FamilyTreeNodeViewModel ConvertToViewModel(FamilyTreeNode node)
        {
            var viewModel = new FamilyTreeNodeViewModel
            {
                Member = node.Member,
                Level = node.Level
            };

            foreach (var child in node.Children)
            {
                viewModel.Children.Add(ConvertToViewModel(child));
            }

            return viewModel;
        }

        private bool CanGenerateFamilyTree()
        {
            return SelectedFamily != null && !IsLoading;
        }

        private bool CanFindRootAncestor()
        {
            return SelectedMember != null && !IsLoading;
        }

        private bool CanFindDescendants()
        {
            return SelectedMember != null && !IsLoading;
        }
    }
}
