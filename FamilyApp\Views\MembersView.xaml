<UserControl x:Class="FamilyApp.Views.MembersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <materialDesign:PackIcon Kind="Account" 
                                   Width="64" Height="64" 
                                   HorizontalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   Margin="0,0,0,20"/>
            <TextBlock Text="إدارة الأفراد" 
                      FontSize="24" 
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,10"/>
            <TextBlock Text="قريباً..." 
                      FontSize="16" 
                      HorizontalAlignment="Center"
                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>
    </Grid>
</UserControl>
