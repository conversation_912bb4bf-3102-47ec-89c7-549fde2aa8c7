<UserControl x:Class="FamilyApp.Views.MembersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:FamilyApp.Converters"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <local:GenderToStringConverter x:Key="GenderToStringConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Account"
                                   Width="24" Height="24"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
            <TextBlock Text="إدارة الأفراد"
                      FontSize="20"
                      FontWeight="Bold"
                      VerticalAlignment="Center"/>

            <Button Content="إضافة فرد جديد"
                   Command="{Binding AddMemberCommand}"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Margin="20,0,0,0"
                   HorizontalAlignment="Left"/>

            <Button Content="تحديث البيانات"
                   Command="{Binding RefreshCommand}"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="10,0,0,0"
                   HorizontalAlignment="Left"/>
        </StackPanel>

        <!-- Search Bar -->
        <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,0,15">
            <StackPanel Orientation="Horizontal">
                <TextBox materialDesign:HintAssist.Hint="البحث في الأعضاء..."
                        Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}"
                        Width="300"
                        Margin="0,0,10,0"/>
                <Button Content="بحث"
                       Command="{Binding SearchMembersCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"/>
                <Button Content="إظهار الكل"
                       Command="{Binding RefreshCommand}"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       Margin="10,0,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <!-- Members List -->
            <materialDesign:Card Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="قائمة الأعضاء" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding Members}"
                             SelectedItem="{Binding SelectedMember}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             materialDesign:DataGridAssist.CellPadding="8"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم الكامل"
                                              Binding="{Binding FullName}"
                                              Width="*"/>
                            <DataGridTextColumn Header="النوع"
                                              Binding="{Binding Gender, Converter={StaticResource GenderToStringConverter}}"
                                              Width="60"/>
                            <DataGridTextColumn Header="تاريخ الميلاد"
                                              Binding="{Binding DateOfBirth, StringFormat=dd/MM/yyyy}"
                                              Width="100"/>
                            <DataGridTextColumn Header="تاريخ الوفاة"
                                              Binding="{Binding DateOfDeath, StringFormat=dd/MM/yyyy}"
                                              Width="100"/>
                            <DataGridTextColumn Header="رقم القبر"
                                              Binding="{Binding GraveNumber}"
                                              Width="80"/>
                            <DataGridTextColumn Header="العائلة"
                                              Binding="{Binding Family.FamilyName}"
                                              Width="*"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تعديل"
                                                   Command="{Binding DataContext.EditMemberCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Margin="0,0,5,0"/>
                                            <Button Content="حذف"
                                                   Command="{Binding DataContext.DeleteMemberCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Foreground="Red"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="3"
               Background="{DynamicResource MaterialDesignDivider}"
               Padding="10,5"
               Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding Members.Count, StringFormat='إجمالي الأعضاء: {0}'}"
                          FontSize="12"/>
                <TextBlock Text="{Binding ErrorMessage}"
                          Foreground="Red"
                          FontSize="12"
                          Margin="20,0,0,0"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>
                <ProgressBar IsIndeterminate="True"
                           Width="100"
                           Height="4"
                           Margin="20,0,0,0"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
