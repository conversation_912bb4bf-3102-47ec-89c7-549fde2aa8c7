<UserControl x:Class="FamilyApp.Views.MembersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:FamilyApp.Converters"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <local:GenderToStringConverter x:Key="GenderToStringConverter"/>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Account"
                                   Width="24" Height="24"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
            <TextBlock Text="إدارة الأفراد"
                      FontSize="20"
                      FontWeight="Bold"
                      VerticalAlignment="Center"/>

            <Button Content="إضافة فرد جديد"
                   Command="{Binding AddMemberCommand}"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Margin="20,0,0,0"
                   HorizontalAlignment="Left"/>
        </StackPanel>

        <!-- Search Bar -->
        <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,0,15">
            <StackPanel Orientation="Horizontal">
                <TextBox materialDesign:HintAssist.Hint="البحث في الأعضاء..."
                        Text="{Binding SearchTerm, UpdateSourceTrigger=PropertyChanged}"
                        Width="300"
                        Margin="0,0,10,0"/>
                <Button Content="بحث"
                       Command="{Binding SearchMembersCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"/>
                <Button Content="إظهار الكل"
                       Command="{Binding LoadMembersCommand}"
                       Style="{StaticResource MaterialDesignFlatButton}"
                       Margin="10,0,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Members List -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="قائمة الأعضاء" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding Members}"
                             SelectedItem="{Binding SelectedMember}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             materialDesign:DataGridAssist.CellPadding="8"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم الكامل"
                                              Binding="{Binding FullName}"
                                              Width="*"/>
                            <DataGridTextColumn Header="النوع"
                                              Binding="{Binding Gender, Converter={StaticResource GenderToStringConverter}}"
                                              Width="80"/>
                            <DataGridTextColumn Header="تاريخ الميلاد"
                                              Binding="{Binding DateOfBirth, StringFormat=dd/MM/yyyy}"
                                              Width="120"/>
                            <DataGridTextColumn Header="العائلة"
                                              Binding="{Binding Family.FamilyName}"
                                              Width="*"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تعديل"
                                                   Command="{Binding DataContext.EditMemberCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Margin="0,0,5,0"/>
                                            <Button Content="حذف"
                                                   Command="{Binding DataContext.DeleteMemberCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Foreground="Red"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Member Form -->
            <materialDesign:Card Grid.Column="1" Margin="10,0,0,0" Padding="15"
                               Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel>
                    <TextBlock Text="{Binding SelectedMember, Converter={StaticResource MemberFormTitleConverter}}"
                              FontSize="16"
                              FontWeight="Bold"
                              Margin="0,0,0,20"/>

                    <!-- Full Name -->
                    <TextBox materialDesign:HintAssist.Hint="الاسم الكامل"
                            materialDesign:HintAssist.IsFloating="True"
                            Text="{Binding FullName, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,0,0,15"/>

                    <!-- Gender Selection -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="النوع" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal">
                            <RadioButton Content="ذكر"
                                       IsChecked="{Binding SelectedGender, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Male}"
                                       Margin="0,0,20,0"/>
                            <RadioButton Content="أنثى"
                                       IsChecked="{Binding SelectedGender, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter=Female}"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Date of Birth -->
                    <DatePicker materialDesign:HintAssist.Hint="تاريخ الميلاد"
                               materialDesign:HintAssist.IsFloating="True"
                               SelectedDate="{Binding DateOfBirth}"
                               Margin="0,0,0,15"/>

                    <!-- Family Selection -->
                    <ComboBox materialDesign:HintAssist.Hint="العائلة"
                             materialDesign:HintAssist.IsFloating="True"
                             ItemsSource="{Binding AvailableFamilies}"
                             SelectedItem="{Binding SelectedFamily}"
                             DisplayMemberPath="FamilyName"
                             Margin="0,0,0,20"/>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="حفظ"
                               Command="{Binding SaveMemberCommand}"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Margin="0,0,10,0"/>
                        <Button Content="إلغاء"
                               Command="{Binding CancelEditCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="3"
               Background="{DynamicResource MaterialDesignDivider}"
               Padding="10,5"
               Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding Members.Count, StringFormat='إجمالي الأعضاء: {0}'}"
                          FontSize="12"/>
                <TextBlock Text="{Binding ErrorMessage}"
                          Foreground="Red"
                          FontSize="12"
                          Margin="20,0,0,0"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>
                <ProgressBar IsIndeterminate="True"
                           Width="100"
                           Height="4"
                           Margin="20,0,0,0"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
