<Window x:Class="FamilyApp.Views.ImageViewerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="{Binding MemberName, StringFormat='صورة {0}'}" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Account" 
                                       Width="24" Height="24" 
                                       VerticalAlignment="Center"
                                       Margin="0,0,10,0"/>
                <TextBlock Text="{Binding MemberName}" 
                          FontSize="18" 
                          FontWeight="Bold"
                          VerticalAlignment="Center"/>
                <TextBlock Text="{Binding MemberDetails}" 
                          FontSize="14" 
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          VerticalAlignment="Center"
                          Margin="20,0,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Image Display Area -->
        <Border Grid.Row="1" 
                Background="{DynamicResource MaterialDesignDivider}"
                Margin="10,0,10,10">
            <Grid>
                <!-- Loading Indicator -->
                <StackPanel HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <ProgressBar IsIndeterminate="True" 
                                Width="100" Height="4"
                                Margin="0,0,0,10"/>
                    <TextBlock Text="جاري تحميل الصورة..." 
                              HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Error Message -->
                <StackPanel HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="ImageBroken" 
                                           Width="64" Height="64"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding ErrorMessage}" 
                              HorizontalAlignment="Center"
                              Foreground="Red"
                              TextWrapping="Wrap"
                              MaxWidth="300"/>
                    <Button Content="اختر صورة أخرى"
                           Command="{Binding SelectImageCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,10,0,0"/>
                </StackPanel>

                <!-- Image Display -->
                <ScrollViewer HorizontalScrollBarVisibility="Auto"
                             VerticalScrollBarVisibility="Auto"
                             Visibility="{Binding HasImage, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Image Source="{Binding ImageSource}"
                          Stretch="Uniform"
                          RenderOptions.BitmapScalingMode="HighQuality"/>
                </ScrollViewer>

                <!-- No Image Placeholder -->
                <StackPanel HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           Visibility="{Binding HasNoImage, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="AccountCircle" 
                                           Width="128" Height="128"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           Margin="0,0,0,20"/>
                    <TextBlock Text="لا توجد صورة لهذا العضو" 
                              HorizontalAlignment="Center"
                              FontSize="16"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              Margin="0,0,0,10"/>
                    <Button Content="إضافة صورة"
                           Command="{Binding SelectImageCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Margin="0,10,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="2" Margin="10" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="تغيير الصورة"
                       Command="{Binding SelectImageCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,10,0"
                       Visibility="{Binding HasImage, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                
                <Button Content="حذف الصورة"
                       Command="{Binding DeleteImageCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Foreground="Red"
                       Margin="0,0,10,0"
                       Visibility="{Binding HasImage, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                
                <Button Content="حفظ التغييرات"
                       Command="{Binding SaveCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="0,0,10,0"
                       Visibility="{Binding HasChanges, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                
                <Button Content="إغلاق"
                       Click="Close_Click"
                       Style="{StaticResource MaterialDesignFlatButton}"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>
