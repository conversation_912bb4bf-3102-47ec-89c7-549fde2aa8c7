using System.Windows;
using System.Windows.Controls;
using FamilyApp.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace FamilyApp.Views
{
    public partial class MembersView : UserControl
    {
        private readonly IServiceProvider _serviceProvider;

        public MembersView(MembersViewModel viewModel, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            DataContext = viewModel;
            _serviceProvider = serviceProvider;

            // Subscribe to add member command
            viewModel.AddMemberRequested += OnAddMemberRequested;
        }

        private void OnAddMemberRequested(object? sender, EventArgs e)
        {
            var addMemberViewModel = _serviceProvider.GetRequiredService<AddMemberViewModel>();
            var addMemberWindow = new AddMemberWindow(addMemberViewModel)
            {
                Owner = Window.GetWindow(this)
            };

            if (addMemberWindow.ShowDialog() == true)
            {
                // Refresh the members list
                if (DataContext is MembersViewModel membersViewModel)
                {
                    membersViewModel.RefreshCommand.Execute(null);
                }
            }
        }
    }
}
