using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using FamilyApp.ViewModels;

namespace FamilyApp.Views
{
    public partial class MembersView : UserControl
    {
        public MembersView(MembersViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        private void SelectPicture_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر صورة",
                Filter = "Image files (*.jpg, *.jpeg, *.png, *.bmp)|*.jpg;*.jpeg;*.png;*.bmp|All files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                if (DataContext is MembersViewModel viewModel)
                {
                    viewModel.PicturePath = openFileDialog.FileName;
                }
            }
        }
    }
}
