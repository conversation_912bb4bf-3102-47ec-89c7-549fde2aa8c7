using System.Windows;
using System.Windows.Controls;
using FamilyApp.ViewModels;
using FamilyApp.Services;
using Microsoft.Extensions.DependencyInjection;

namespace FamilyApp.Views
{
    public partial class MembersView : UserControl
    {
        private readonly IServiceProvider _serviceProvider;

        public MembersView(MembersViewModel viewModel, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            DataContext = viewModel;
            _serviceProvider = serviceProvider;

            // Subscribe to events
            viewModel.AddMemberRequested += OnAddMemberRequested;
            viewModel.ViewImageRequested += OnViewImageRequested;
        }

        private void OnAddMemberRequested(object? sender, EventArgs e)
        {
            var addMemberViewModel = _serviceProvider.GetRequiredService<AddMemberViewModel>();
            var addMemberWindow = new AddMemberWindow(addMemberViewModel)
            {
                Owner = Window.GetWindow(this)
            };

            if (addMemberWindow.ShowDialog() == true)
            {
                // Refresh the members list
                if (DataContext is MembersViewModel membersViewModel)
                {
                    membersViewModel.RefreshCommand.Execute(null);
                }
            }
        }

        private void OnViewImageRequested(object? sender, Models.Member member)
        {
            var imageViewerViewModel = new ImageViewerViewModel(
                _serviceProvider.GetRequiredService<DataService>(),
                _serviceProvider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<ImageViewerViewModel>>(),
                member);

            var imageViewerWindow = new ImageViewerWindow(imageViewerViewModel)
            {
                Owner = Window.GetWindow(this)
            };

            imageViewerWindow.ShowDialog();
        }
    }
}
