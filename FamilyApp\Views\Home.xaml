<Window x:Class="FamilyApp.Views.Home"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام الأنساب" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <materialDesign:Card Grid.Column="0"
                           Margin="10">
            <StackPanel>
                <!-- Header -->
                <Border Background="{DynamicResource PrimaryHueMidBrush}" 
                       Padding="20">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="Family" 
                                               Width="32" Height="32" 
                                               Foreground="White"
                                               HorizontalAlignment="Center"/>
                        <TextBlock Text="نظام الأنساب" 
                                  Foreground="White"
                                  FontSize="16" 
                                  FontWeight="Bold"
                                  HorizontalAlignment="Center"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Navigation Menu -->
                <StackPanel Margin="0,20,0,0">
                    <!-- Families -->
                    <Button Command="{Binding NavigateToFamiliesCommand}"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="20,15"
                           Margin="5,2"
                           Cursor="Hand">
                        <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                            <TextBlock Text="👨‍👩‍👧‍👦" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="العائلات" FontSize="14" Foreground="Black"/>
                        </StackPanel>
                    </Button>

                    <!-- Members -->
                    <Button Command="{Binding NavigateToMembersCommand}"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="20,15"
                           Margin="5,2"
                           Cursor="Hand">
                        <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                            <TextBlock Text="👤" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="الأفراد" FontSize="14" Foreground="Black"/>
                        </StackPanel>
                    </Button>

                    <!-- Reports -->
                    <Button Command="{Binding NavigateToReportsCommand}"
                           Background="Transparent"
                           BorderThickness="0"
                           HorizontalAlignment="Stretch"
                           HorizontalContentAlignment="Right"
                           Padding="20,15"
                           Margin="5,2"
                           Cursor="Hand">
                        <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
                            <TextBlock Text="📊" FontSize="16" Margin="0,0,10,0"/>
                            <TextBlock Text="التقارير" FontSize="14" Foreground="Black"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Main Content Area -->
        <materialDesign:Card Grid.Column="1"
                           Margin="0,10,10,10">
            <ContentPresenter Content="{Binding CurrentView}"/>
        </materialDesign:Card>
    </Grid>
</Window>
