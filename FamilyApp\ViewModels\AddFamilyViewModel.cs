using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public partial class AddFamilyViewModel : BaseViewModel
    {
        private readonly DataService _dataService;

        [ObservableProperty]
        private string familyName = string.Empty;

        [ObservableProperty]
        private Member? selectedFather;

        [ObservableProperty]
        private Member? selectedMother;

        [ObservableProperty]
        private ObservableCollection<Member> availableFathers = new();

        [ObservableProperty]
        private ObservableCollection<Member> availableMothers = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        public event EventHandler<bool>? CloseRequested;

        public AddFamilyViewModel(DataService dataService)
        {
            _dataService = dataService;
            LoadDataAsync();
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (string.IsNullOrWhiteSpace(FamilyName))
            {
                ErrorMessage = "يرجى إدخال اسم العائلة";
                return;
            }

            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var newFamily = new Family
                {
                    FamilyName = FamilyName.Trim(),
                    FatherId = SelectedFather?.Id,
                    MotherId = SelectedMother?.Id
                };

                await _dataService.CreateFamilyAsync(newFamily);
                CloseRequested?.Invoke(this, true);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في حفظ العائلة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                var members = await _dataService.GetAllMembersAsync();
                
                var fathers = members.Where(m => m.Gender == Gender.Male).ToList();
                var mothers = members.Where(m => m.Gender == Gender.Female).ToList();
                
                AvailableFathers = new ObservableCollection<Member>(fathers);
                AvailableMothers = new ObservableCollection<Member>(mothers);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
}
