using System.Windows;
using System.Windows.Controls;
using FamilyApp.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace FamilyApp.Views
{
    public partial class FamiliesView : UserControl
    {
        private readonly IServiceProvider _serviceProvider;

        public FamiliesView(FamiliesViewModel viewModel, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            DataContext = viewModel;
            _serviceProvider = serviceProvider;

            // Subscribe to add family command
            viewModel.AddFamilyRequested += OnAddFamilyRequested;
        }

        private void OnAddFamilyRequested(object? sender, EventArgs e)
        {
            var addFamilyViewModel = _serviceProvider.GetRequiredService<AddFamilyViewModel>();
            var addFamilyWindow = new AddFamilyWindow(addFamilyViewModel)
            {
                Owner = Window.GetWindow(this)
            };

            if (addFamilyWindow.ShowDialog() == true)
            {
                // Refresh the families list
                if (DataContext is FamiliesViewModel familiesViewModel)
                {
                    familiesViewModel.RefreshCommand.Execute(null);
                }
            }
        }
    }
}
