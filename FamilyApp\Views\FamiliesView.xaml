<UserControl x:Class="FamilyApp.Views.FamiliesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="AccountGroup" 
                                   Width="24" Height="24" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
            <TextBlock Text="إدارة العائلات" 
                      FontSize="20" 
                      FontWeight="Bold"
                      VerticalAlignment="Center"/>
            
            <Button Content="إضافة عائلة جديدة"
                   Command="{Binding AddFamilyCommand}"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Margin="20,0,0,0"
                   HorizontalAlignment="Left"/>

            <Button Content="تحديث البيانات"
                   Command="{Binding LoadFamiliesCommand}"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="10,0,0,0"
                   HorizontalAlignment="Left"/>
        </StackPanel>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Families List -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="قائمة العائلات" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                    
                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding Families}"
                             SelectedItem="{Binding SelectedFamily}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             materialDesign:DataGridAssist.CellPadding="8"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="اسم العائلة" 
                                              Binding="{Binding FamilyName}" 
                                              Width="*"/>
                            <DataGridTextColumn Header="الأب" 
                                              Binding="{Binding Father.FullName}" 
                                              Width="*"/>
                            <DataGridTextColumn Header="الأم" 
                                              Binding="{Binding Mother.FullName}" 
                                              Width="*"/>
                            <DataGridTextColumn Header="عدد الأفراد" 
                                              Binding="{Binding Members.Count}" 
                                              Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="تعديل"
                                                   Command="{Binding DataContext.EditFamilyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Margin="0,0,5,0"/>
                                            <Button Content="حذف"
                                                   Command="{Binding DataContext.DeleteFamilyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   Style="{StaticResource MaterialDesignFlatButton}"
                                                   Foreground="Red"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Family Form -->
            <materialDesign:Card Grid.Column="1" Margin="10,0,0,0" Padding="15"
                               Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel>
                    <TextBlock Text="{Binding SelectedFamily, Converter={StaticResource FamilyFormTitleConverter}}" 
                              FontSize="16" 
                              FontWeight="Bold" 
                              Margin="0,0,0,20"/>

                    <!-- Family Name -->
                    <TextBox materialDesign:HintAssist.Hint="اسم العائلة"
                            materialDesign:HintAssist.IsFloating="True"
                            Text="{Binding FamilyName, UpdateSourceTrigger=PropertyChanged}"
                            Margin="0,0,0,15"/>

                    <!-- Father Selection -->
                    <ComboBox materialDesign:HintAssist.Hint="الأب"
                             materialDesign:HintAssist.IsFloating="True"
                             ItemsSource="{Binding AvailableFathers}"
                             SelectedItem="{Binding SelectedFather}"
                             DisplayMemberPath="FullName"
                             Margin="0,0,0,15"/>

                    <!-- Mother Selection -->
                    <ComboBox materialDesign:HintAssist.Hint="الأم"
                             materialDesign:HintAssist.IsFloating="True"
                             ItemsSource="{Binding AvailableMothers}"
                             SelectedItem="{Binding SelectedMother}"
                             DisplayMemberPath="FullName"
                             Margin="0,0,0,20"/>

                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="حفظ"
                               Command="{Binding SaveFamilyCommand}"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Margin="0,0,10,0"/>
                        <Button Content="إلغاء"
                               Command="{Binding CancelEditCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" 
               Background="{DynamicResource MaterialDesignDivider}" 
               Padding="10,5"
               Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding Families.Count, StringFormat='إجمالي العائلات: {0}'}" 
                          FontSize="12"/>
                <TextBlock Text="{Binding ErrorMessage}" 
                          Foreground="Red" 
                          FontSize="12"
                          Margin="20,0,0,0"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>
                <ProgressBar IsIndeterminate="True"
                           Width="100"
                           Height="4"
                           Margin="20,0,0,0"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
