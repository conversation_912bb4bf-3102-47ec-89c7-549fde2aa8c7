2025-07-15 21:12:21.917 +02:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 21:12:22.127 +02:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'FamilyAppDb001'
2025-07-15 21:12:22.260 +02:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 21:12:22.353 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `Members` AS `m`)
2025-07-15 21:12:30.724 +02:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 21:12:30.796 +02:00 [INF] Executed DbCommand (19ms) [Parameters=[@__username_0='admin' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `u`.`Id`, `u`.`CreatedAt`, `u`.`PasswordHash`, `u`.`Username`
FROM `Users` AS `u`
WHERE `u`.`Username` = @__username_0
LIMIT 1
2025-07-15 21:12:30.991 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:12:31.001 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:12:31.154 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:12:31.198 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:12:38.016 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:12:38.016 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:12:38.125 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:12:38.128 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:12:39.928 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:12:40.062 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:12:42.022 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:12:46.064 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:12:51.315 +02:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='1'], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `Members`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-07-15 21:12:51.333 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:12:57.232 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:13:14.728 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:13:20.164 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:13:20.300 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:13:22.059 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:13:22.060 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:13:22.176 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:13:22.177 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
