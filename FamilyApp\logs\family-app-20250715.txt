2025-07-15 19:53:14.634 +02:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'FamilyAppDb001'
2025-07-15 19:53:14.731 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `Members` AS `m`)
2025-07-15 19:55:46.122 +02:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'FamilyAppDb001'
2025-07-15 19:55:46.218 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `Members` AS `m`)
2025-07-15 21:02:45.878 +02:00 [INF] Executed DbCommand (16ms) [Parameters=[@__username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT `u`.`Id`, `u`.`CreatedAt`, `u`.`PasswordHash`, `u`.`Username`
FROM `Users` AS `u`
WHERE `u`.`Username` = @__username_0
LIMIT 1
2025-07-15 21:02:46.120 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:02:46.128 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:02:46.135 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:02:46.229 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:02:54.928 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:02:54.929 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:02:54.929 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:02:54.942 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:02:56.882 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:02:56.895 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:02:58.798 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:02:58.799 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:03:00.493 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:03:00.507 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:03:16.458 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:03:35.134 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:03:35.151 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:03:40.100 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[@__searchTerm_0_contains='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
WHERE `m`.`FullName` LIKE @__searchTerm_0_contains
2025-07-15 21:03:45.869 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:03:45.884 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:03:47.969 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:03:47.990 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:03:48.951 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:03:48.952 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:03:48.953 +02:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:03:48.971 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:03:51.603 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:03:51.603 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:03:51.605 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:03:51.614 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:03:54.753 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:04:06.570 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:04:06.571 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:04:07.584 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:04:07.616 +02:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:04:11.633 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:04:11.633 +02:00 [ERR] An exception occurred while iterating over the results of a query for context type 'FamilyApp.Database.AppDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-15 21:04:11.633 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
2025-07-15 21:04:11.653 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:05:41.889 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`
FROM `Members` AS `m`
LEFT JOIN `Families` AS `f` ON `m`.`FamilyId` = `f`.`Id`
2025-07-15 21:05:41.918 +02:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `f`.`Id`, `f`.`CreatedAt`, `f`.`FamilyName`, `f`.`FatherId`, `f`.`MotherId`, `m`.`Id`, `m`.`CreatedAt`, `m`.`DateOfBirth`, `m`.`DateOfDeath`, `m`.`FamilyId`, `m`.`FullName`, `m`.`Gender`, `m`.`GraveNumber`, `m`.`GravePlace`, `m`.`PicturePath`, `m0`.`Id`, `m0`.`CreatedAt`, `m0`.`DateOfBirth`, `m0`.`DateOfDeath`, `m0`.`FamilyId`, `m0`.`FullName`, `m0`.`Gender`, `m0`.`GraveNumber`, `m0`.`GravePlace`, `m0`.`PicturePath`, `m1`.`Id`, `m1`.`CreatedAt`, `m1`.`DateOfBirth`, `m1`.`DateOfDeath`, `m1`.`FamilyId`, `m1`.`FullName`, `m1`.`Gender`, `m1`.`GraveNumber`, `m1`.`GravePlace`, `m1`.`PicturePath`
FROM `Families` AS `f`
LEFT JOIN `Members` AS `m` ON `f`.`FatherId` = `m`.`Id`
LEFT JOIN `Members` AS `m0` ON `f`.`MotherId` = `m0`.`Id`
LEFT JOIN `Members` AS `m1` ON `f`.`Id` = `m1`.`FamilyId`
ORDER BY `f`.`Id`, `m`.`Id`, `m0`.`Id`
