using System.IO;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FamilyApp.Models;
using FamilyApp.Services;

namespace FamilyApp.ViewModels
{
    public partial class ImageViewerViewModel : BaseViewModel
    {
        private readonly DataService _dataService;
        private readonly ILogger<ImageViewerViewModel> _logger;
        private readonly Member _member;

        [ObservableProperty]
        private string memberName = string.Empty;

        [ObservableProperty]
        private string memberDetails = string.Empty;

        [ObservableProperty]
        private BitmapImage? imageSource;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool hasError = false;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private bool hasChanges = false;

        [ObservableProperty]
        private string? newImagePath;

        public bool HasImage => ImageSource != null && !HasError;
        public bool HasNoImage => ImageSource == null && !HasError && !IsLoading;

        public event EventHandler? CloseRequested;

        public ImageViewerViewModel(DataService dataService, ILogger<ImageViewerViewModel> logger, Member member)
        {
            _dataService = dataService;
            _logger = logger;
            _member = member;

            MemberName = member.FullName;
            MemberDetails = $"{(member.Gender == Gender.Male ? "ذكر" : "أنثى")} • {member.DateOfBirth?.ToString("yyyy/MM/dd") ?? "غير محدد"}";

            LoadImageAsync();
        }

        [RelayCommand]
        private async Task SelectImageAsync()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختر صورة",
                    Filter = "Image files (*.jpg, *.jpeg, *.png, *.bmp, *.gif)|*.jpg;*.jpeg;*.png;*.bmp;*.gif|All files (*.*)|*.*",
                    FilterIndex = 1
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    _logger.LogInformation("User selected image: {ImagePath}", openFileDialog.FileName);
                    
                    NewImagePath = openFileDialog.FileName;
                    await LoadImageFromPathAsync(NewImagePath);
                    HasChanges = true;
                    
                    OnPropertyChanged(nameof(HasImage));
                    OnPropertyChanged(nameof(HasNoImage));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting image for member {MemberId}", _member.Id);
                ShowError($"خطأ في اختيار الصورة: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task DeleteImageAsync()
        {
            try
            {
                _logger.LogInformation("Deleting image for member {MemberId}", _member.Id);
                
                ImageSource = null;
                NewImagePath = null;
                HasChanges = true;
                HasError = false;
                ErrorMessage = string.Empty;
                
                OnPropertyChanged(nameof(HasImage));
                OnPropertyChanged(nameof(HasNoImage));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting image for member {MemberId}", _member.Id);
                ShowError($"خطأ في حذف الصورة: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            try
            {
                IsLoading = true;
                _logger.LogInformation("Saving image changes for member {MemberId}", _member.Id);

                // Copy image to app directory if needed
                string? finalImagePath = null;
                if (!string.IsNullOrEmpty(NewImagePath))
                {
                    var appImagesDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images");
                    Directory.CreateDirectory(appImagesDir);
                    
                    var fileName = $"{_member.Id}_{Path.GetFileName(NewImagePath)}";
                    finalImagePath = Path.Combine(appImagesDir, fileName);
                    
                    File.Copy(NewImagePath, finalImagePath, true);
                    _logger.LogInformation("Image copied to: {FinalPath}", finalImagePath);
                }

                // Update member in database
                _member.PicturePath = finalImagePath;
                await _dataService.UpdateMemberAsync(_member);
                
                HasChanges = false;
                _logger.LogInformation("Image saved successfully for member {MemberId}", _member.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving image for member {MemberId}", _member.Id);
                ShowError($"خطأ في حفظ الصورة: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadImageAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                ErrorMessage = string.Empty;

                if (!string.IsNullOrEmpty(_member.PicturePath))
                {
                    await LoadImageFromPathAsync(_member.PicturePath);
                }
                
                OnPropertyChanged(nameof(HasImage));
                OnPropertyChanged(nameof(HasNoImage));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading image for member {MemberId}", _member.Id);
                ShowError($"خطأ في تحميل الصورة: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadImageFromPathAsync(string imagePath)
        {
            await Task.Run(() =>
            {
                try
                {
                    if (!File.Exists(imagePath))
                    {
                        throw new FileNotFoundException("الملف غير موجود");
                    }

                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    bitmap.Freeze();

                    App.Current.Dispatcher.Invoke(() =>
                    {
                        ImageSource = bitmap;
                        HasError = false;
                        ErrorMessage = string.Empty;
                    });

                    _logger.LogInformation("Image loaded successfully: {ImagePath}", imagePath);
                }
                catch (Exception ex)
                {
                    App.Current.Dispatcher.Invoke(() =>
                    {
                        ShowError($"فشل في تحميل الصورة: {ex.Message}");
                    });
                    throw;
                }
            });
        }

        private void ShowError(string message)
        {
            HasError = true;
            ErrorMessage = message;
            ImageSource = null;
            
            OnPropertyChanged(nameof(HasImage));
            OnPropertyChanged(nameof(HasNoImage));
        }
    }
}
