<UserControl x:Class="FamilyApp.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="ChartTree"
                                   Width="24" Height="24"
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
            <TextBlock Text="التقارير والإحصائيات"
                      FontSize="20"
                      FontWeight="Bold"
                      VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Report Selection -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Family Tree Report -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="شجرة العائلة" FontWeight="Bold" Margin="0,0,0,10"/>
                    <ComboBox materialDesign:HintAssist.Hint="اختر العائلة"
                             ItemsSource="{Binding Families}"
                             SelectedItem="{Binding SelectedFamily}"
                             DisplayMemberPath="FamilyName"
                             Margin="0,0,0,10"/>
                    <Button Content="إنشاء شجرة العائلة"
                           Command="{Binding GenerateFamilyTreeCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           HorizontalAlignment="Stretch"/>
                </StackPanel>

                <!-- Root Ancestor Report -->
                <StackPanel Grid.Column="1" Margin="10,0">
                    <TextBlock Text="جذر الفرد" FontWeight="Bold" Margin="0,0,0,10"/>
                    <ComboBox materialDesign:HintAssist.Hint="اختر الفرد"
                             ItemsSource="{Binding Members}"
                             SelectedItem="{Binding SelectedMember}"
                             DisplayMemberPath="FullName"
                             Margin="0,0,0,10"/>
                    <Button Content="البحث عن الجذر"
                           Command="{Binding FindRootAncestorCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           HorizontalAlignment="Stretch"/>
                </StackPanel>

                <!-- Descendants Report -->
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="أحفاد الفرد" FontWeight="Bold" Margin="0,0,0,10"/>
                    <ComboBox materialDesign:HintAssist.Hint="اختر الفرد"
                             ItemsSource="{Binding Members}"
                             SelectedItem="{Binding SelectedMember}"
                             DisplayMemberPath="FullName"
                             Margin="0,0,0,10"/>
                    <Button Content="البحث عن الأحفاد"
                           Command="{Binding FindDescendantsCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           HorizontalAlignment="Stretch"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Results Area -->
        <TabControl Grid.Row="2" materialDesign:ColorZoneAssist.Mode="PrimaryMid">
            <!-- Family Tree Tab -->
            <TabItem Header="شجرة العائلة">
                <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                    <TreeView ItemsSource="{Binding FamilyTree}" Margin="20">
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal" Margin="5">
                                    <materialDesign:PackIcon Kind="Account"
                                                           Width="16" Height="16"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding DisplayName}"
                                              FontWeight="Bold"
                                              VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding GenderDisplay, StringFormat=' ({0})'}"
                                              VerticalAlignment="Center"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    <TextBlock Text="{Binding DateOfBirthDisplay, StringFormat=' - {0}'}"
                                              VerticalAlignment="Center"
                                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                    </TreeView>
                </ScrollViewer>
            </TabItem>

            <!-- Root Ancestor Tab -->
            <TabItem Header="جذر الفرد">
                <StackPanel Margin="20">
                    <TextBlock Text="الجذر الأعلى للفرد المحدد:"
                              FontSize="16"
                              FontWeight="Bold"
                              Margin="0,0,0,15"/>

                    <materialDesign:Card Padding="15"
                                       Visibility="{Binding RootAncestor, Converter={StaticResource NullToVisibilityConverter}}">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                <materialDesign:PackIcon Kind="AccountStar"
                                                       Width="20" Height="20"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,10,0"/>
                                <TextBlock Text="{Binding RootAncestor.FullName}"
                                          FontSize="18"
                                          FontWeight="Bold"
                                          VerticalAlignment="Center"/>
                            </StackPanel>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="النوع:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding RootAncestor.Gender, Converter={StaticResource GenderToStringConverter}}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الميلاد:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding RootAncestor.DateOfBirth, StringFormat=dd/MM/yyyy}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="العائلة:" FontWeight="Bold" Margin="0,0,10,0"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding RootAncestor.Family.FamilyName}"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </TabItem>

            <!-- Descendants Tab -->
            <TabItem Header="أحفاد الفرد">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0"
                              Text="أحفاد الفرد المحدد:"
                              FontSize="16"
                              FontWeight="Bold"
                              Margin="0,0,0,15"/>

                    <DataGrid Grid.Row="1"
                             ItemsSource="{Binding Descendants}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             materialDesign:DataGridAssist.CellPadding="8"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم الكامل"
                                              Binding="{Binding FullName}"
                                              Width="*"/>
                            <DataGridTextColumn Header="النوع"
                                              Binding="{Binding Gender, Converter={StaticResource GenderToStringConverter}}"
                                              Width="80"/>
                            <DataGridTextColumn Header="تاريخ الميلاد"
                                              Binding="{Binding DateOfBirth, StringFormat=dd/MM/yyyy}"
                                              Width="120"/>
                            <DataGridTextColumn Header="العائلة"
                                              Binding="{Binding Family.FamilyName}"
                                              Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <Border Grid.Row="3"
               Background="{DynamicResource MaterialDesignDivider}"
               Padding="10,5"
               Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding Families.Count, StringFormat='العائلات: {0}'}"
                          FontSize="12"
                          Margin="0,0,20,0"/>
                <TextBlock Text="{Binding Members.Count, StringFormat='الأعضاء: {0}'}"
                          FontSize="12"
                          Margin="0,0,20,0"/>
                <TextBlock Text="{Binding ErrorMessage}"
                          Foreground="Red"
                          FontSize="12"
                          Margin="20,0,0,0"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"/>
                <ProgressBar IsIndeterminate="True"
                           Width="100"
                           Height="4"
                           Margin="20,0,0,0"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
