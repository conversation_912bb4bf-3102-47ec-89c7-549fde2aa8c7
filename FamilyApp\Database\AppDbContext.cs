using Microsoft.EntityFrameworkCore;
using FamilyApp.Models;

namespace FamilyApp.Database
{
    public class AppDbContext : DbContext
    {
        public DbSet<User> Users { get; set; }
        public DbSet<Family> Families { get; set; }
        public DbSet<Member> Members { get; set; }

        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PasswordHash).IsRequired().HasMaxLength(255);
            });

            // Configure Member entity
            modelBuilder.Entity<Member>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Gender).IsRequired();
                entity.Property(e => e.GraveNumber).HasMaxLength(50);
                entity.Property(e => e.GravePlace).HasMaxLength(200);
                entity.Property(e => e.PicturePath).HasMaxLength(500);

                // Configure relationship with Family (members belonging to family)
                entity.HasOne(e => e.Family)
                      .WithMany(f => f.Members)
                      .HasForeignKey(e => e.FamilyId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Family entity
            modelBuilder.Entity<Family>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FamilyName).IsRequired().HasMaxLength(100);
                
                // Configure Father relationship
                entity.HasOne(e => e.Father)
                      .WithMany(m => m.FamiliesAsFather)
                      .HasForeignKey(e => e.FatherId)
                      .OnDelete(DeleteBehavior.SetNull);
                
                // Configure Mother relationship
                entity.HasOne(e => e.Mother)
                      .WithMany(m => m.FamiliesAsMother)
                      .HasForeignKey(e => e.MotherId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"), // Default password
                    CreatedAt = DateTime.Now
                }
            );
        }
    }
}
