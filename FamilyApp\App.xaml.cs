﻿using System.Configuration;
using System.Data;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using FamilyApp.Database;
using FamilyApp.Services;
using FamilyApp.ViewModels;
using FamilyApp.Views;

namespace FamilyApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Configure database
                services.AddDbContext<AppDbContext>(options =>
                    options.UseMySql(
                        "Server=localhost;Database=FamilyAppDb;Uid=root;Pwd=;",
                        new MySqlServerVersion(new Version(8, 0, 21))
                    ));

                // Register services
                services.AddScoped<DataService>();
                services.AddScoped<ReportService>();
                services.AddScoped<DatabaseSeeder>();

                // Register ViewModels
                services.AddTransient<LoginViewModel>();
                services.AddTransient<HomeViewModel>();
                services.AddTransient<FamiliesViewModel>();
                services.AddTransient<MembersViewModel>();
                services.AddTransient<ReportsViewModel>();

                // Register Views
                services.AddTransient<LoginPage>();
                services.AddTransient<Home>();
                services.AddTransient<FamiliesView>();
                services.AddTransient<MembersView>();
                services.AddTransient<ReportsView>();
            })
            .Build();

        // Ensure database is created and seeded
        using (var scope = _host.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            context.Database.EnsureCreated();

            var seeder = scope.ServiceProvider.GetRequiredService<DatabaseSeeder>();
            seeder.SeedAsync().Wait();
        }

        // Show login window
        var loginPage = _host.Services.GetRequiredService<LoginPage>();
        loginPage.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }
}

