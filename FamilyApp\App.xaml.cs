﻿using System.Configuration;
using System.Data;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using FamilyApp.Database;
using FamilyApp.Services;
using FamilyApp.ViewModels;
using FamilyApp.Views;

namespace FamilyApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.File("logs/family-app-.txt",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 30,
                outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        _host = Host.CreateDefaultBuilder()
            .UseSerilog() // Add Serilog
            .ConfigureServices((context, services) =>
            {
                // Configure database with factory pattern
                services.AddDbContextFactory<AppDbContext>(options =>
                    options.UseMySql(
                        "Server=localhost;Database=FamilyAppDb001;Uid=root;Pwd=;",
                        new MySqlServerVersion(new Version(8, 0, 21))
                    ).EnableSensitiveDataLogging()
                    .EnableServiceProviderCaching(false)
                    .EnableDetailedErrors());

                // Register services
                services.AddScoped<DataService>();
                services.AddScoped<ReportService>();
                services.AddScoped<DatabaseSeeder>();

                // Register ViewModels
                services.AddTransient<LoginViewModel>();
                services.AddTransient<HomeViewModel>();
                services.AddTransient<FamiliesViewModel>();
                services.AddTransient<MembersViewModel>();
                services.AddTransient<ReportsViewModel>();
                services.AddTransient<AddFamilyViewModel>();
                services.AddTransient<AddMemberViewModel>();
                services.AddTransient<ImageViewerViewModel>();

                // Register Views
                services.AddTransient<LoginPage>();
                services.AddTransient<Home>();
                services.AddTransient<FamiliesView>();
                services.AddTransient<MembersView>();
                services.AddTransient<ReportsView>();
                services.AddTransient<AddFamilyWindow>();
                services.AddTransient<AddMemberWindow>();
                services.AddTransient<ImageViewerWindow>();
            })
            .Build();

        // Ensure database is created and seeded
        using (var scope = _host.Services.CreateScope())
        {
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            context.Database.EnsureCreated();

            var seeder = scope.ServiceProvider.GetRequiredService<DatabaseSeeder>();
            seeder.SeedAsync().Wait();
        }

        // Show login window
        var loginPage = _host.Services.GetRequiredService<LoginPage>();
        loginPage.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }
}

